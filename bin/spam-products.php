<?php declare(strict_types = 1);

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\Supply\Supply;
use App\Model\Orm\Tree\CatalogTree;
use Brick\Money\Money;
use Nette\Utils\Strings;

require __DIR__ . '/../vendor/autoload.php';

$container = App\Bootstrap::boot()->createContainer();
$orm = $container->getByType(Orm::class);

$cs = $orm->mutation->getByIdChecked(1);
$stocks = $orm->stock->findAll();
$priceLevels = $orm->priceLevel->findAll();

$productCount = 1000;



// CATEGORIES

function createCategory(
	Orm $orm,
	Mutation $mutation,
	CatalogTree $parent,
	string $name,
): CatalogTree
{
	$category = new CatalogTree();
	$category->type = $category::TYPE_CATALOG;
	$category->template = ':Front:Catalog:default';
	$category->rootId = $mutation->rootId;
	$category->mutation = $mutation;
	$category->parent = $parent;
	$category->pathString = "$parent->pathString|$parent->id|";
	$category->name = $name;
	$category->nameAnchor = $name;
	$category->nameTitle = $name;
	$category->created = 1;
	$category->public = true;
	$orm->tree->persistAndFlush($category);

	$category->setAlias(Strings::webalize($name));

	echo ".";

	return $category;
}

$eshop = $orm->tree->getByIdChecked(21);
assert($eshop instanceof CatalogTree);

$categories = (function () use ($orm, $cs, $eshop) {
	echo "Spamming categories";

	$categories = [];
	$categories[] = $c1 = createCategory($orm, $cs, $eshop, 'Kategorie 1');
	$categories[] = $c1_1 = createCategory($orm, $cs, $c1, 'Kategorie 1.1');
	$categories[] = createCategory($orm, $cs, $c1_1, 'Kategorie 1.1.1');
	$categories[] = createCategory($orm, $cs, $c1, 'Kategorie 1.2');
	$categories[] = $c2 = createCategory($orm, $cs, $eshop, 'Kategorie 2');
	$categories[] = createCategory($orm, $cs, $c2, 'Kategorie 2.1');
	$categories[] = $c2_2 = createCategory($orm, $cs, $c2, 'Kategorie 2.2');
	$categories[] = createCategory($orm, $cs, $c2_2, 'Kategorie 2.2.1');
	$categories[] = $c3 = createCategory($orm, $cs, $eshop, 'Kategorie 3');
	$categories[] = $c3_1 = createCategory($orm, $cs, $c3, 'Kategorie 3.1');
	$categories[] = createCategory($orm, $cs, $c3_1, 'Kategorie 3.1.1');
	$categories[] = createCategory($orm, $cs, $c3_1, 'Kategorie 3.1.2');
	$categories[] = createCategory($orm, $cs, $c3, 'Kategorie 3.2');

	echo " DONE\n";

	return $categories;
})();


// PARAMETERS

function createParameter(
	string $name,
	bool $variantParameter = false,
): Parameter
{
	$parameter = new Parameter();
	$parameter->type = Parameter::TYPE_SELECT;
	$parameter->name = $name;
	$parameter->uid = Strings::webalize($name);
	$parameter->isInFilter = 1;
	$parameter->variantParameter = (int) $variantParameter;

	echo ".";

	return $parameter;
}

[$allParameters, $variantParameter, $nonVariantParameters] = (function () use ($orm, $eshop) {
	echo "Spamming parameters";

	$parameters = $nonVariantParameters = [];
	$parameters[] = $variantParameter = createParameter('Parametr 1', variantParameter: true);
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 2');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 3');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 4');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 5');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 6');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 7');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 8');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 9');
	$parameters[] = $nonVariantParameters[] = createParameter('Parametr 10');

	foreach ($parameters as $parameter) {
		$orm->parameter->persist($parameter);
	}

	$orm->parameter->flush();

	$visibleParameters = array_map(fn(Parameter $parameter) => ['parameter' => (string) $parameter->id], $parameters);
	$eshop->setCf(['parameterForFilter' => [['visibleParameters' => $visibleParameters]]]);
	$orm->tree->flush();

	echo " DONE\n";

	return [$parameters, $variantParameter, $nonVariantParameters];
})();



// PARAMETER VALUES

function createParameterValue(
	Parameter $parameter,
	string $value,
): ParameterValue
{
	$parameterValue = new ParameterValue();
	$parameterValue->parameter = $parameter;
	$parameterValue->internalValue = $value;
	$parameterValue->internalAlias = Strings::webalize($value);

	echo ".";

	return $parameterValue;
}

function createParameterValues(
	Parameter $parameter,
	int $parameterNumber,
	int $count,
): array
{
	$values = [];
	for ($i = 1; $i <= $count; $i++) {
		$values[] = createParameterValue($parameter, "Hodnota $parameterNumber.$i");
	}

	return $values;
}

$parameterValuesByParameter = (function () use ($allParameters, $orm) {
	echo "Spamming parameter values";

	$parameterValuesByParameter = [];
	foreach ($allParameters as $index => $parameter) {
		$parameterValuesByParameter[$parameter->id] = $parameterValues = createParameterValues($parameter, $index + 1, random_int(2, 100));
		foreach ($parameterValues as $parameterValue) {
			$orm->parameterValue->persist($parameterValue);
		}
	}

	$orm->parameterValue->flush();

	echo " DONE\n";

	return $parameterValuesByParameter;
})();

$variantParameterValues = $parameterValuesByParameter[$variantParameter->id];





// PRODUCTS

(function () use ($orm, $cs, $productCount, $categories, $stocks, $priceLevels, $nonVariantParameters, $parameterValuesByParameter, $variantParameterValues) {
	echo "Spamming products";

	for ($i = 1; $i <= $productCount; $i++) {
		$product = new Product();
		$product->template = ':Front:Product:detail';
		$product->public = 1;
		$product->internalName = "Produkt $i";
		$orm->product->persist($product);

		$variants = [];
		$variantParameterValuesCount = count($variantParameterValues);
		for ($vp = 0; $vp < max(1, random_int(-$variantParameterValuesCount, $variantParameterValuesCount)); $vp++) {
			$variants[] = $variant = new ProductVariant();
			$variant->product = $product;
			$variant->param1Value = $variantParameterValues[$vp];
			$orm->productVariant->persist($variant);
		}

		$randomCategory = array_rand($categories);
		$productTree = new ProductTree();
		$productTree->product = $product;
		$productTree->tree = $categories[$randomCategory];
		$productTree->sort = 0;
		$orm->productTree->persist($productTree);

		$localization = new ProductLocalization();
		$localization->mutation = $cs;
		$localization->product = $product;
		$localization->public = 1;
		$localization->name = "Produkt $i";
		$localization->nameAnchor = "Produkt $i";
		$localization->nameTitle = "Produkt $i";
		$orm->productLocalization->persistAndFlush($localization);
		$localization->setAlias("produkt-$i");

		foreach ($variants as $variant) {
			$variantLocalization = new ProductVariantLocalization();
			$variantLocalization->mutation = $cs;
			$variantLocalization->variant = $variant;
			$variantLocalization->active = 1;
			$orm->productVariantLocalization->persist($variantLocalization);

			foreach ($stocks as $stock) {
				$supply = new Supply();
				$supply->variant = $variant;
				$supply->stock = $stock;
				$supply->amount = max(0, random_int(-30, 50));
				$orm->supply->persist($supply);
			}

			foreach ($priceLevels as $priceLevel) {
				$variantPrice = new ProductVariantPrice();
				$variantPrice->productId = $product->id;
				$variantPrice->productVariant = $variant;
				$variantPrice->mutation = $cs;
				$variantPrice->priceLevel = $priceLevel;
				$variantPrice->price = Price::from(Money::of(random_int(1, 9999), $cs->currency));
				$orm->productVariantPrice->persist($variantPrice);
			}
		}

		foreach ($nonVariantParameters as $parameter) {
			$orm->product->addParameterValue($product, $parameterValuesByParameter[$parameter->id][array_rand($parameterValuesByParameter[$parameter->id])]);
		}

		$orm->flush();

		echo ".";

		$orm->product->doClear();
		$orm->productLocalization->doClear();
		$orm->productVariant->doClear();
		$orm->productVariantLocalization->doClear();
		$orm->productVariantPrice->doClear();
		$orm->productTree->doClear();
		$orm->supply->doClear();
	}

	echo " DONE\n";
})();



// ELASTIC

$cs = $orm->mutation->getByIdChecked(1);
$container->getByType(MutationHolder::class)->setMutation($cs);
$csProductIndex = $orm->esIndex->getBy(['type' => EsIndex::TYPE_PRODUCT, 'mutation' => $cs]);
if ($csProductIndex !== null) {
	$esIndexFacade = $container->getByType(EsIndexFacade::class);
	echo "Spamming everything into ES index $csProductIndex->name ...";
	$esIndexFacade->fill($csProductIndex);
}

echo ". DONE\n";
