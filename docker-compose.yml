networks:
  kornfeil:
  superkoders:
      external: true

volumes:
  db:
  es:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: kornfeil_app
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - kornfeil
      - superkoders
    labels:
        - "traefik.enable=true"
        - "traefik.docker.network=superkoders"
        - "traefik.http.routers.kornfeil.rule=Host(`kornfeil.superkoders.test`)"
        - "traefik.http.routers.kornfeil.tls=true"
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - db
      - es
      - front
      - admin

  front:
    build:
      dockerfile: docker/front/Dockerfile
      context: .
    container_name: kornfeil_front
    restart: unless-stopped
    networks:
      - kornfeil
    volumes:
      - .:/app

  admin:
    build:
      dockerfile: docker/admin/Dockerfile
      context: .
    container_name: kornfeil_admin
    restart: unless-stopped
    networks:
      - kornfeil
    volumes:
      - .:/app

  adminer:
    image: adminer
    restart: always
    networks:
      - kornfeil
    ports:
      - "81:8080"

  db:
    image: mariadb:10
    hostname: kornfeil_db
    container_name: kornfeil_db
    restart: unless-stopped
    networks:
      - kornfeil
    ports:
      - "3306:3306"
    volumes:
        - db:/var/lib/mysql
        - ./docker/db:/docker/db
        - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
    environment:
      MARIADB_ROOT_PASSWORD: 'root'
      MARIADB_DATABASE: 'kornfeil'
      MARIADB_USER: 'kornfeil'
      MARIADB_PASSWORD: 'kornfeil'

  db-init:
      image: mariadb:10
      container_name: kornfeil_db-init
      depends_on:
          - db
      networks:
          - kornfeil
      volumes:
          - ./docker/db/init-db.sh:/docker/db/init-db.sh
          - ./docker/db:/docker/db
      environment:
          MARIADB_HOST: 'kornfeil_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'kornfeil'
          MARIADB_USER: 'kornfeil'
          MARIADB_PASSWORD: 'kornfeil'
      entrypoint: [ "/docker/db/init-db.sh" ]

  es:
    image: elasticsearch:7.17.6
    hostname: kornfeil_es
    container_name: kornfeil_es
    restart: unless-stopped
    networks:
      - kornfeil
    ports:
      - "9200:9200"
    volumes:
      - es:/usr/share/elasticsearch/data
    environment:
      "discovery.type": single-node

  redis:
    image: redis:latest
    hostname: kornfeil_redis
    container_name: kornfeil_redis
    restart: unless-stopped
    networks:
      - kornfeil
    ports:
      - "6379:6379"

  mailcatcher:
    image: dockage/mailcatcher
    hostname: kornfeil_mailcatcher
    container_name: kornfeil_mailcatcher
    restart: unless-stopped
    networks:
      - kornfeil
    ports:
      - "1080:1080"
