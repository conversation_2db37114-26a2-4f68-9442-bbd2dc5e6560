import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
	static values = {
		number: Number,
		duration: { type: Number, default: 2000 },
	};

	connect() {
		this.observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					this.startCounting();
					this.observer.disconnect();
				}
			},
			{
				threshold: 0.6,
			},
		);

		this.observer.observe(this.element);
	}

	startCounting() {
		const el = this.element;
		const target = this.numberValue;
		const steps = 60;
		const interval = this.durationValue / steps;

		let current = 0;
		let step = Math.ceil(target / steps);

		const targetLength = target.toLocaleString('cs-CZ').replace(/\xa0/g, ' ').length;

		const tick = () => {
			current += step;
			if (current > target) current = target;

			let formatted = current.toLocaleString('cs-CZ').replace(/\xa0/g, ' ');

			while (formatted.length < targetLength) {
				formatted = '0' + formatted;
			}

			el.innerText = formatted;

			if (current < target) {
				setTimeout(tick, interval);
			}
		};

		tick();
	}
}
