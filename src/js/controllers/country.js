import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['select', 'name', 'flag'];

		change = () => {
			var val = this.selectTarget.value;
			var text = this.selectTarget.options[this.selectTarget.selectedIndex].text;

			this.flagTarget.setAttribute('src', `/static/img/flags/${val}.svg`);
			this.nameTarget.innerHTML = text;
		};
	};
};
