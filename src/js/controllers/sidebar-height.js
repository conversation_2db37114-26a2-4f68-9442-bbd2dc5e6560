import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
	static targets = ['sidebar'];

	connect() {
		this.updateHeights();

		this.resizeObserver = new ResizeObserver(() => this.updateHeights());

		if (this.hasSidebarTarget) {
			this.resizeObserver.observe(this.sidebarTarget);
		}

		if (window.lenis) {
			this.scrollHandler = () => this.updateHeights();
			window.lenis.on('scroll', this.scrollHandler);
		}
	}

	disconnect() {
		if (this.resizeObserver) {
			if (this.hasSidebarTarget) this.resizeObserver.unobserve(this.sidebarTarget);
		}

		if (window.lenis && this.scrollHandler) {
			window.lenis.off('scroll', this.scrollHandler);
		}
	}

	updateHeights() {
		if (this.hasSidebarTarget) {
			const height = this.sidebarTarget.getBoundingClientRect().height;
			document.documentElement.style.setProperty('--sidebar-height', `${height}px`);
		}
	}
}
