@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-count {
	position: relative;
	display: inline-block;
	width: 9rem;
	&__inp.inp-text {
		min-height: 4rem;
		padding: 0 3rem;
		text-align: center;
	}
	&__tool {
		position: absolute;
		top: 0.1rem;
		bottom: 0.1rem;
		width: 3.6rem;
		transition: background-color variables.$t;
		-webkit-tap-highlight-color: transparent;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 1.2rem;
			height: 0.2rem;
			border-radius: 0.1rem;
			background: variables.$color-primary;
			transition: background-color variables.$t;
			transform: translate(-50%, -50%);
		}
		&--minus {
			left: 0;
		}
		&--plus {
			right: 0;
			&::after {
				transform: translate(-50%, -50%) rotate(90deg);
			}
		}
		&.is-disabled {
			cursor: default;
			&::before,
			&::after {
				background: variables.$color-bd;
			}
		}
	}

	// STATES
	&.has-error &__inp {
		outline-color: variables.$color-secondary;
	}

	// HOVERS
	.hoverevents &__tool:not(.is-disabled):hover {
		&::before,
		&::after {
			background: variables.$color-hover;
		}
	}
}
