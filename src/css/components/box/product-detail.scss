@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/typography';
@use 'base/extends';
@use 'base/mixins';

.b-product-detail {
	$s: &;
	padding: clamp(12.5rem, calc(136 / 1920 * 100vw), 25.6rem) 0 0;

	&__wrapper {
		display: grid;
		align-items: flex-start;
	}

	&__title,
	&__annot {
		margin-top: functions.dynamic-size(64);
	}

	&__carousel {
		overflow: visible;
	}

	&__gallery {
		position: relative;
		padding: functions.dynamic-size(100) functions.dynamic-size(64) functions.dynamic-size(176);
		background-image: linear-gradient(0deg, #f1ece2 0%, #f5f0e6 74%);

		.embla__container {
			display: flex;
			gap: functions.dynamic-size(32);
		}

		.embla__slide {
			display: flex;
			flex: 0 0 100%;
			justify-content: center;
			align-items: center;
			max-width: 100%;

			img {
				display: block;
				width: 100%;
				max-width: functions.dynamic-size(561);
				height: 100%;
				aspect-ratio: 561 / 626;
				object-fit: contain;
			}
		}
	}

	&__controls {
		position: absolute;
		inset: 0;
		z-index: 1;
		display: flex;
		align-content: center;
		justify-content: space-between;
		width: 100%;
		margin-bottom: functions.dynamic-size(70);
		padding: functions.dynamic-size(64) functions.dynamic-size(30);

		& > * {
			pointer-events: auto;
		}
	}

	&__cta {
		position: absolute;
		bottom: functions.dynamic-size(57);
		left: 0;
		z-index: 1;
		display: flex;
		justify-content: center;
		width: 100%;
		pointer-events: auto;

		a {
			--color-link: currentcolor;
			--color-link-decoration: transparent;
			--color-hover: var(--color-link);
		}

		.btn__text {
			flex-wrap: wrap;
		}
	}

	&__image {
		width: 100%;
		height: auto;
	}

	&__tech-title,
	&__benefits-title {
		@extend %pp-20;
		margin-bottom: functions.dynamic-size(16);
		font-family: variables.$font-primary;
		font-weight: 600;
		text-transform: none;
	}

	&__tech-title {
		margin-top: functions.dynamic-size(64);
	}

	&__benefits-title {
		margin-top: functions.dynamic-size(100);
	}

	&__tech-list {
		display: grid;
		grid-template-columns: 1fr 1fr;
		row-gap: functions.dynamic-size(16);
		margin: 0;

		dt,
		dd {
			margin: 0;
			padding-top: functions.dynamic-size(6);
			padding-bottom: functions.dynamic-size(16);
			border-top: 0.1rem solid variables.$color-bd;
		}

		dd {
			padding-left: functions.dynamic-size(10);
		}
	}

	&__benefits-list {
		margin: 0;
		padding: functions.dynamic-size(10) 0 0;
		border-top: 0.1rem solid variables.$color-bd;
		list-style: none;

		li {
			position: relative;
			margin-bottom: functions.dynamic-size(28);

			&::before {
				content: '';
				position: absolute;
				top: 50%;
				left: 0;
				display: inline-block;
				width: functions.dynamic-size(18);
				height: functions.dynamic-size(2);
				background: variables.$color-primary;
				background: #321919;
				transform: translateY(-50%);
			}
		}
	}

	&__download {
		@extend %pp-20;
		display: flex;
		flex-wrap: wrap;
		row-gap: functions.dynamic-size(20);
		justify-content: space-between;
		column-gap: functions.dynamic-size(40);
		margin-top: functions.dynamic-size(75);
		font-weight: 600;

		p {
			flex: 1 0 functions.dynamic-size(200);
		}
	}

	&__download-btn {
		flex: 0 0 auto;
	}

	&__perex {
		@extend %pp-18;
		margin-top: functions.dynamic-size(64);
		padding-top: functions.dynamic-size(12);
		border-top: 0.1rem solid variables.$color-bd;
	}

	// MQ
	@media (config.$xl-down) {
		&__wrapper {
			row-gap: functions.dynamic-size(40);
		}

		&__benefits-list {
			li {
				padding-left: functions.dynamic-size(43);
			}
		}
	}

	@media (config.$xl-up) {
		&__wrapper {
			grid-template-columns: 50% 35%;
			column-gap: functions.dynamic-size(136);
		}

		&__gallery {
			grid-area: 1/1/3/1;
		}

		&__header {
			grid-area: 1/2/1/2;
		}

		&__content {
			grid-area: 2/2/3/2;
			margin-top: functions.dynamic-size(70);
		}

		&__header,
		&__content {
			padding-left: functions.dynamic-size(43);
		}

		&__benefits-list {
			li {
				&::before {
					left: calc(-1 * functions.dynamic-size(43));
				}
			}
		}
	}
}
