@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-benefits {
	container-type: inline-size;
	&__list {
		@extend %reset-ul;
		@include mixins.grid-layout();
		gap: clamp(functions.spacing('lg'), calc(100 / 1920 * 100vw), 10rem) var(--grid-gutter);
	}
	&__item {
		@extend %reset-ul-li;
		grid-column: auto / span 12;
	}
	&__holder {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		max-width: clamp(25rem, calc(330 / 1920 * 100vw), 33rem);
		height: 100%;
		min-height: clamp(0rem, calc(203 / 1920 * 100vw), 20.3rem);
		padding-left: 1.8rem;
		border-left: 0.1rem solid variables.$color-black;
	}
	&__title {
		margin: 0 0 functions.spacing('lg');
	}

	// MQ
	@container (min-width: 375px) {
		&:has(.pp-24) &__item {
			grid-column: auto / span 6;
		}
	}
	@container (min-width: 480px) {
		&__item {
			grid-column: auto / span 6;
		}
	}
	@container (min-width: 1200px) {
		&:has(.pp-24) &__item,
		&__item {
			grid-column: auto / span 3;
		}
	}
}
