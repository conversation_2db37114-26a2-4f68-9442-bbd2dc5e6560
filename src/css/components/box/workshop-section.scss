@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-workshop-section {
	padding-top: functions.spacing('sm');
	border-top: 0.1rem solid variables.$color-bd;
	&:first-child {
		padding: 0;
		border: none;
	}
	&__left {
		grid-column: auto / span var(--section-title-cols);
		margin: 0 0 functions.spacing('lg');
	}
	&__content {
		grid-column: auto / span var(--section-content-cols);
	}
	&__img {
		max-width: 18.5rem;
	}

	// MQ
	@media (config.$lg-down) {
		&__title br {
			display: none;
		}
	}
	@media (config.$lg-up) {
		--section-title-cols: 2;
		--section-content-cols: 7;
		@include mixins.grid-layout();
		grid-template-columns: repeat(var(--content-cols), minmax(0, 1fr));
		&__left {
			margin: 0;
		}
	}
	@media (config.$xl-up) {
		--section-title-cols: 3;
		--section-content-cols: 6;
	}
}
