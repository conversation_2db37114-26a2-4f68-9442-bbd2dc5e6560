@use 'base/variables';

.f-search {
	position: relative;
	max-width: 50rem;
	margin: 0 auto;
	.inp-icon__icon {
		.icon-svg {
			transition: opacity variables.$t;
		}
	}
	&__wrap {
		display: flex;
		margin: 0;
		.inp-fix {
			flex: 1;
		}
	}
	&__loader {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 1.6rem;
		height: 1.6rem;
		margin: -0.8rem 0 0 -0.8rem;
		border: 0.2rem solid;
		border-top-color: transparent;
		border-radius: 2rem;
		opacity: 0;
		transition: opacity variables.$t;
	}

	// STATEs
	&__inp.is-loading ~ .inp-icon__icon {
		.icon-svg {
			opacity: 0;
		}
		.f-search__loader {
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}
