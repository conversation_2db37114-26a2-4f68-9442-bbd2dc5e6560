@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/typography';
@use 'base/extends';
@use 'base/mixins';

.c-events {
	overflow: hidden;
	&__title {
		margin-bottom: clamp(0rem, calc(195 / 1920 * 100vw), 19.5rem);
	}
	&__top {
		display: flex;
		flex-wrap: wrap;
		gap: functions.spacing('sm');
		justify-content: space-between;
		align-items: center;
		pointer-events: auto;
	}
	&__title2 {
		max-width: clamp(29rem, calc(400 / 1920 * 100vw), 40rem);
	}
	&__tools {
		display: flex;
		gap: 1.4rem;
	}
	&__carousel {
		overflow: visible;
	}
	&__list {
		--grid-x-spacing: clamp(var(--grid-gutter), calc(24 / 1920 * 100vw), 2.4rem);
	}
	&__item {
		--item-width: clamp(30rem, calc(740 / 1920 * 100vw), 74rem);
		width: calc(var(--item-width) + var(--grid-x-spacing));
	}
}
