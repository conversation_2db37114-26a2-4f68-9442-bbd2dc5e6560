<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;

class TopScoreData implements Convertor
{

	private ?Parameter $manufacturerParameter = null;

	public function __construct(
		private ParameterRepository $parameterRepository,
		private PriceLevelModel $priceLevelModel,
	)
	{
	}

	public function convert(Product $product): array
	{
		$mutation = $product->getMutation();

		$priceLevels = $this->priceLevelModel->getAllPriceLevel()->findBy(['type' => PriceLevel::TYPE_DEFAULT]);

		$data = [];

		foreach ($mutation->states as $state) {
			foreach ($priceLevels as $priceLevel) {
				assert($priceLevel instanceof PriceLevel);
				$data['topScore'][$state->code][$priceLevel->type] = $this->getTopScore($product, $mutation, $priceLevel, $state);
			}
		}

		return $data;
	}


	private function getTopScore(Product $product, Mutation $mutation, PriceLevel $priceLevel, State $state): float
	{
		$pages = $mutation->pages;

		if ($this->manufacturerParameter === null) {
			$this->manufacturerParameter = $this->parameterRepository->getBy(['uid' => Parameter::UID_MANUFACTURER]);
		}

		$ownBrand = 1;

		$soldCount = 1;
		if ($product->soldCount > 0) {
			$soldCount = $product->soldCount;
		}

		$isNew = 1;
		if ($product->isNew === 1) {
			$isNew = ($pages->eshop->cf->topScore->isNew ?? 1);
		}

		$sortBoost = 0;
		if (isset($product->cf->sortBoost) && $product->cf->sortBoost) {
			$sortBoost = $product->cf->sortBoost;
		}

		$isAction = 1;
//		if ($product->discountPrice($mutation, $priceLevel, $state) !== null) {
//			$isAction = ($pages->eshop->cf->topScore->isAction ?? 1);
//		}

		return $soldCount + $ownBrand * $isNew * $isAction + $sortBoost;
	}

}
