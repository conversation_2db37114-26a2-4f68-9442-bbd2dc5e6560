<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;

class BlogData implements Convertor
{

	public const TYPE = 'blog';

	public function convert(object $object): array
	{
		assert($object instanceof BlogLocalization);

		return [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
			'publicFrom' => ConvertorHelper::convertTime($object->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($object->publicTo),
		];
	}

}
