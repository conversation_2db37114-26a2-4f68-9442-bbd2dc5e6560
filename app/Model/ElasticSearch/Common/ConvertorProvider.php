<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Common\Convertor\BlogData;
use App\Model\ElasticSearch\Common\Convertor\TreeData;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		private BlogData $blogData,
		private TreeData $treeData
	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(string $class): array
	{
		return match ($class) {

			BlogLocalization::class => [
				$this->blogData,
			],
			CatalogTree::class, Tree::class, CommonTree::class => [
				$this->treeData,
			],
			default => throw new LogicException(sprintf("Missing common definition for '%s' class", $class))
		};
	}

	public function getAllLikeStrings(string $class): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll($class));
	}

}
