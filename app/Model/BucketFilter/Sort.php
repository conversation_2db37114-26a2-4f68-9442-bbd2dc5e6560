<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;

final class Sort
{

	private array $sentences = [];

	public function addByPrice(State $state, PriceLevel $priceLevel, string $dir = 'asc'): void
	{
//		$this->sentences["hasSomePrice"] = 'desc';
		$this->sentences["statePricesWithVat.{$state->code}.{$priceLevel->type}"] = $dir;
	}

	public function addByName(string $dir = 'asc'): void
	{
		$this->sentences['nameSort'] = $dir;
	}

	public function getSentences(): array
	{
		return $this->sentences;
	}

	public function addByStore(): void
	{
		$this->sentences['isInStore'] = 'desc';
	}

	public function addByTopScore(State $state, PriceLevel $priceLevel): void
	{
		$this->sentences["topScore.{$state->code}.{$priceLevel->type}"] = 'desc';
	}

	public function addByBestsellere(): void
	{
		$this->sentences['soldCount'] = 'desc';
	}

	public function addByScore(): void
	{
		$this->sentences['_score'] = 'desc';
	}

	public function addByReview(): void
	{
		$this->sentences['reviewAverage'] = 'desc';
	}

}
