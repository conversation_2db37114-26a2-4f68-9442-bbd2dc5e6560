<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\Range;
use App\Model\BucketFilter\ElasticItem\SingleValue;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;

class ElasticItemList implements ElasticItemListGenerator
{

	private ?array $list;

	public function __construct(
		private readonly array $allSelectedParameters,
		private readonly State $currentState,
		private readonly PriceLevel $priceLevel,
		private readonly QueryFilter $queryFilter,
	)
	{
	}



	public function getElasticItemList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getItemsForCatalog();
		}

		return $this->list;
	}


	private function getItemsForCatalog(): array
	{
		$filterItems = [];

		$name = 'price';
		$priceItem = new Range(
			$this->queryFilter,
			$name,
			(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'] : null,
			(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'] : null,
		);
		$priceItem->setElasticPath(['statePricesWithVat', $this->currentState->code, $this->priceLevel->type]);
		$filterItems[] = $priceItem;

		$name = 'path';
		$filterItems[] = new SingleValue(
			$name,
			$this->allSelectedParameters[DiscreteValues::NAMESPACE_FLAG_VALUES][$name] ?? [],
			$this->queryFilter,
		);

		return $filterItems;
	}


}
