<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryAggregation;
use App\Model\BucketFilter\QueryFilter;
use App\Model\Orm\Parameter\Parameter;
use Closure;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\QueryBuilder;

abstract class DiscreteValues implements ElasticItem, QuestionableElasticItem, AggregableElasticItem
{

	public const NAMESPACE_DIALS = 'dials';
	public const NAMESPACE_FLAGS = 'flags';
	public const NAMESPACE_FLAG_VALUES = 'flagValues';

	protected string $elasticPath;

	/**
	 * @phpstan-param  null|Closure(mixed): (int|bool|string) $typeInElasticEnforcer
	 */
	public function __construct(
		protected readonly string $elasticKey,
		protected readonly array $selectedValues,
		protected readonly QueryFilter $queryFilter,
		protected ?Closure $typeInElasticEnforcer = null,
		protected readonly string $operatorBetweenValues = QueryAggregation::OPERATOR_OR,
		protected readonly bool $filterEmptySelectedValues = false, // dont ignore empty $selectedValues

	)
	{
		$this->elasticPath = $elasticKey;
	}

	public function setElasticPath(array $path): void
	{
		$this->elasticPath = implode('.', $path);
	}

	public function getCondition(): ?AbstractQuery
	{
		if (!$this->filterEmptySelectedValues && $this->selectedValues === []) {
			return null;
		}

		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		if ($this->operatorBetweenValues === QueryAggregation::OPERATOR_OR) {
			$terms = new Terms($this->elasticPath);
			foreach ($this->selectedValues as $selectedValue) {
				//$selectedValue = array_map(fn($value) => $this->typeInElasticEnforcer($value), $selectedValue);
				$terms->addTerm($this->enforcerElasticType($selectedValue));
			}

			$boolQuery->addMust($terms);

		} else {
			foreach ($this->selectedValues as $selectedValue) {
				$boolQuery->addMust((new Term())->setTerm($this->elasticPath, $this->enforcerElasticType($selectedValue)));
			}
		}

		return $boolQuery;
	}

	public function getElasticKey(): string
	{
		return $this->elasticKey;
	}


	abstract public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation;

	public function getAggregationName(): string
	{
		return $this->elasticKey . '_agg';
	}


	public function getSelectedValues(): array
	{
		return $this->selectedValues;
	}


	protected function enforcerElasticType(int|float|string|bool $value): int|float|string|bool
	{
		if ($this->typeInElasticEnforcer instanceof Closure) {
			$function = $this->typeInElasticEnforcer;
		} else {
			$function = fn($value) => (int) $value;
		}

		return $function($value);
	}

}
