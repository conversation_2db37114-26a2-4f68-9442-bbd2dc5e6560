<?php declare(strict_types = 1);

namespace App\Model\Image\Storage;

use App\Model\Image\BetterImageTypeDetector;
use App\Model\Image\MissingImageException;
use App\Model\Image\Resizer;
use App\Model\Image\Rotator;
use App\Model\Image\Setup\ImageSizeConfiguration;
use App\Model\Orm\CopyMachine;
use App\Model\Orm\Creator;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use Nette\Http\FileUpload;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use function floor;
use function str_ends_with;

class BasicStorage
{
	/**
	 * To relieve the filesystem, uploaded files are sufficiently evenly distributed into subdirectories. An MD5 hash
	 * of the filename is computed, and subdirectories are created, using the hash as their name.
	 *
	 * This constant determines the number of characters for each subdirectory, and thus the number of buckets (16^N).
	 */
	private const HASH_BUCKET_KEY_LENGTH = 1;

	/**
	 * This constant determines how many subdirectories to create in depth, creating even more buckets to fit billions
	 * of files.
	 */
	private const HASH_BUCKET_DEPTH = 1;

	public const FILE_WITHOUT_RESIZE = [
		'svg',
		'gif',
	];

	public function __construct(
		private readonly array $setups,
		private readonly string $documentRoot,
		private readonly string $mainImageDirectory,
		private readonly Rotator $rotator,
		public readonly ?string $forcedDomain = null,
	)
	{
	}


	public function save(Image $image, string $pathToResampledImage, ImageSizeConfiguration $setup): void
	{
		$pathInfo = pathinfo($pathToResampledImage);
		\assert(isset($pathInfo['dirname']));

		if (!file_exists($pathInfo['dirname'])) {
			mkdir($pathInfo['dirname'], 0777, true);
		}

		$quality = $this->sanitizeImageQuality($pathToResampledImage, $setup->quality);

		$image->save($pathToResampledImage, $quality);
	}


	public function existOriginal(string $fileName, string $extension): bool
	{
		return file_exists($this->getPathToOriginalImage($fileName, $extension));
	}


	public function exist(ImageSizeConfiguration $setup, string $fileName, string $extension): bool
	{
		return file_exists($this->getPathToImage($setup, $fileName, $extension));
	}


	public function getPathToImage(ImageSizeConfiguration $setup, string $fileName, string $extension): ?string
	{
		return sprintf('%s%s/%s.%s', $this->documentRoot, $this->getDirectory($setup, $fileName, $extension), $fileName, $extension);
	}

	public function getPathToOriginalImage(string $fileName, string $extension): string
	{
		return sprintf('%s%s/%s.%s', $this->documentRoot, $this->getOriginalDirectory($fileName), $fileName, $extension);
	}

	public function getImageSrc(ImageSizeConfiguration $setup, string $fileName, string $extension, ?int $timestamp = null): string
	{
		$src = sprintf('%s/%s.%s', $this->getDirectory($setup, $fileName, $extension), $fileName, $extension);
		if ($this->forcedDomain !== '') {
			$src = $this->forcedDomain . $src;
		}

		if ($timestamp !== null) {
			$src = $src . '?ts=' . $timestamp;
		}

		return $src;
	}

	private function getDirectory(ImageSizeConfiguration $setup, string $fileName, string $extension): string
	{
		if (in_array($extension, self::FILE_WITHOUT_RESIZE)) {
			$directory = $this->getOriginalDirectory($fileName);
		} else {
			$directory = sprintf('%s-%s', $this->mainImageDirectory, $setup->type);
			$directory = $this->appendHashBucket($directory, $fileName);
		}

		if (!Strings::startsWith($directory, '/')) {
			$directory = '/' . $directory;
		}
		return $directory;
	}

	private function getOriginalDirectory(string $fileName): string
	{
		return $this->appendHashBucket($this->mainImageDirectory, $fileName);
	}

	private function appendHashBucket(string $directory, string $fileName): string
	{
		$hash = md5($fileName);

		$appendDirSeparator = str_ends_with($directory, '/');

		$bucketParts = [];
		for ($i = 0; $i < self::HASH_BUCKET_DEPTH; $i++) {
			$bucketParts[] = substr($hash, $i * self::HASH_BUCKET_KEY_LENGTH, self::HASH_BUCKET_KEY_LENGTH);
		}

		return rtrim($directory, '/') . '/' . join('/', $bucketParts) . ($appendDirSeparator ? '/' : '');
	}

	public function deleteOriginal(string $filename, string $extension): void
	{
		if ($this->existOriginal($filename, $extension)) {
			$path = $this->getPathToOriginalImage($filename, $extension);
			unlink($path);
		}
	}

	public function deleteAllSizes(string $filename, string $extension): void
	{
		if (!in_array($extension, self::FILE_WITHOUT_RESIZE)) {
			foreach ($this->setups as $sizeName => $setupArray) {
				$setup = ImageSizeConfiguration::fromArray($sizeName, $setupArray);

				if ($this->exist($setup, $filename, $extension)) {
					$path = $this->getPathToImage($setup, $filename, $extension);
					unlink($path);
				}

				foreach (BetterImageTypeDetector::OTHER_IMAGE_EXTENSIONS as $otherImageExtention) {
					if ($this->exist($setup, $filename, $otherImageExtention)) {
						$path = $this->getPathToImage($setup, $filename, $otherImageExtention);
						unlink($path);
					}
				}
			}
		}
	}

	public function hasImageActions(): bool
	{
		return $this->forcedDomain === '' || $this->forcedDomain === null;
	}

	public function detectRotation(FileUpload $file): void
	{
		$this->rotator->normalizeRotation($file->getTemporaryFile(), $file->getSuggestedExtension());
	}


	public function rotate(LibraryImage $libraryImage, int $angle = 90): void
	{
		// rotate original
		$pathToOriginalImage = $this->getPathToOriginalImage($libraryImage->fileNameWithoutExtension, $libraryImage->ext);
		$netteImage = Image::fromFile($pathToOriginalImage);
		$netteImage->rotate($angle, 0);
		$quality = $this->sanitizeImageQuality($pathToOriginalImage, 100);

		$netteImage->save($pathToOriginalImage, $quality);
		//delete versions
		$this->deleteAllSizes($libraryImage->fileNameWithoutExtension, $libraryImage->ext);
	}


	public function sanitizeImageQuality(string $pathToResampledImage, int $quality): int
	{
		if (str_ends_with($pathToResampledImage, '.png')) {
			$quality = (int)floor(0.09 * $quality);
		}
		return $quality;
	}

}
