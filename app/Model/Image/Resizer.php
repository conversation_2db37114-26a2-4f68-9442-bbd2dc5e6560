<?php declare(strict_types = 1);

namespace App\Model\Image;

use App\Model\Image\Storage\BasicStorage;
use GdImage;
use Nette\Utils\Image;
use Nette\Utils\ImageException;
use Nette\Utils\UnknownImageFileException;
use function assert;

class Resizer
{



	private readonly array $envelopeColors;

	public function __construct(
		private readonly string $wwwDir,
		private readonly BasicStorage $storage
	)
	{
		$this->envelopeColors = [
			'red' => 255,
			'green' => 255,
			'blue' => 255,
			'alfa' => 0,
		];
	}

	public function resample(string $fileName, string $extension, Setup\ImageSizeConfiguration $setup, ?string $forceExtension = null): Image
	{
		$pathToOriginalImage = $this->storage->getPathToOriginalImage($fileName, $extension);
		$pathToResampledImage = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);

		try {
			$image = @Image::fromFile($pathToOriginalImage);
		} catch (ImageException) {
			// malformed image
			throw new UnknownImageFileException();
		}

		if ($setup->keepRatio) {
			$image = $image->resize($setup->width, $setup->height, Image::FILL);
			$image = $this->crop($image, '50%', '50%', $setup->width, $setup->height);
		} elseif (($image->getWidth() > $setup->width) || ($image->getHeight() > $setup->height)) {
			// kdyz je obrazek v nekterem rozmeru vetsi -> resize
			if ($setup->fill) {
				$image = $image->resize($setup->width, $setup->width, Image::FILL);
				$image = $image->crop('50%', '50%', $setup->width, $setup->width);
			} else {
				$image = $image->resize($setup->width, $setup->height);
			}
		} else {
			// nedochazi k resizu, rozmery obrazku jsou mensi nez pozadovane, normalizace barev pro png
			if ($extension === 'png') {
				$image->paletteToTrueColor();
			}
		}

		if ($setup->square) {
			$blank = Image::fromBlank($setup->width, $setup->height, Image::rgb($this->envelopeColors['red'], $this->envelopeColors['green'], $this->envelopeColors['blue'], $this->envelopeColors['alfa'])); // @phpstan-ignore-line
			$blank->place($image, (int) ($setup->width / 2 - $image->getWidth() / 2), (int) ($setup->height / 2 - $image->getHeight() / 2));
			$image = $blank;
		}

		if ($setup->watermark !== '') {
			$marked = Image::fromFile($this->wwwDir . $setup->watermark);
			$image->place($marked, (int) ($image->getWidth() / 2 - $marked->getWidth() / 2), (int) ($image->getHeight() / 2 - $marked->getHeight() / 2));
		}

		$this->storage->save($image, $pathToResampledImage, $setup);
		return $image;
	}


	public function crop(Image $image, string $left, string $top, int $width, int $height): Image
	{
		[$r['x'], $r['y'], $r['width'], $r['height']]
			= Image::calculateCutout($image->getWidth(), $image->getHeight(), $left, $top, $width, $height);

		$newImage = Image::fromBlank($r['width'], $r['height'], Image::rgb(255, 255, 255, 127))->getImageResource();
		assert($newImage instanceof GdImage);

		imagealphablending($newImage, false);
		imagesavealpha($newImage, true);

		$imageResource = $image->getImageResource();
		assert($imageResource instanceof GdImage);

		imagecopy($newImage, $imageResource, 0, 0, $r['x'], $r['y'], $r['width'], $r['height']);

		return new Image($newImage);
	}

}
