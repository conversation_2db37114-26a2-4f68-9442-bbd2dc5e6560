<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ProductVariantMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'product_variant';

	/**
	 * @param Tree|null $category
	 * @return ICollection|ProductVariant[]
	 */
	public function findBestseller(Tree $category = null): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from('product_variant', 'pv');

		if ($category) {
			$builder->joinInner('[product] as p', '[p.id] = [pv.productId]');
			$builder->joinInner('[product_tree] as pt', 'p.public = 1 and [p.id] = [pt.productId] AND [pt.treeId] = %i', $category->id);

			$builder->orderBy('pv.soldCount desc, p.soldCount desc, pv.productId');
		} else {
			$builder->orderBy('pv.soldCount desc, pv.productId');
		}

		return $this->toCollection($builder);
	}

	/**
	 * @param array $variantsIds
	 * @return ICollection|ProductVariant[]
	 */
	public function findFilteredVariants(array $variantsIds): ICollection
	{
		$builder = $this->builder()->select('p.*')->from('product_variant', 'p')
			->andWhere('id in %i[]', $variantsIds)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $variantsIds) . ')');
		return $this->toCollection($builder);
	}

}
