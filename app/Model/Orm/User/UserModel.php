<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserImage\UserImage;
use Nette\Security\Passwords;
use Nextras\Dbal\Utils\DateTimeImmutable;
use App\Model\CustomField\CustomFields;

final class UserModel
{
	public function __construct(
		private readonly Orm $orm,
		private readonly CustomFields $customFields,
		private readonly Passwords $passwords,
	) {}


	public function delete(User $user): void
	{
		$this->orm->removeAndFlush($user);
	}


	public function save(User $user, mixed $data, ?int $userId = null, string $action = 'edit'): User
	{
		// tyto hodnoty jsou bool a DB je chce v integeru
		$intValues = [];
		$boolValues = []; //'showPriceMy', 'showPriceBrutto', 'isBlock'
		$noSave = ['id', 'userMutations'];

		if (($data['password'] ?? '') === '') {
			unset($data['password']);
			unset($data['passwordVerify']);
		}

//		if (isset($data['preferLang']) && $data['preferLang'] && $user->preferLang != $data['preferLang']) {
//			$this->response->setCookie(LangDetector::COOKIE_KEY, 0, '300 days', null, null, true, true);
//		}

		if (($data['ic'] ?? '') !== '') {
			$data['ic'] = mb_substr($data['ic'], 0, 20);
		}

		if (($data['dic'] ?? '') !== '') {
			$data['dic'] = mb_substr($data['dic'], 0, 20);
		}

		if ($action === 'create') {
			if ($userId) {
				$data['created'] = $userId;
			}

			$data['createdTime'] = new DateTimeImmutable();

		} else {
			if ($userId) {
				$data['edited'] = $userId;
				$data['editedTime'] = new DateTimeImmutable();
			}
		}

		foreach ($user->getMetadata()->getProperties() as $i) {
			$col = $i->name;
			if (isset($data[$col])) {
				if ($col === 'password') {
					if (($data[$col] ?? '') !== '') {
						$data[$col] = $this->passwords->hash($data[$col]);
						$user->setValue($col, $data[$col]);
					} else {
						$user->setValue($col, $user->password);
					}

					continue;
				}

				/** @phpstan-ignore-next-line */
				if (in_array($col, $intValues, true)) {
					$data[$col] = (int) $data[$col];
				}

				if (!in_array($col, $noSave, true)) {
					$user->setValue($col, $data[$col]);
				}
			} else {
				/** @phpstan-ignore-next-line */
				if (in_array($col, $boolValues, true)) {
					$user->setValue($col, 0);
				}
			}
		}

		if ($userId) {
			if (isset($data['customFields'])) {
				$user->cf = $this->customFields->prepareDataToSave($data['customFields']);
			}

			$this->handleMutations($user, $data);
		}

		$this->orm->user->persistAndFlush($user);

		return $user;
	}

	private function handleMutations(User $user, array $data): void
	{
		if (isset($data['userMutations'])) {
			$mutations = [];

			if (!in_array($data['userMutations'], [null, '', []])) {
				foreach ($this->orm->mutation->findBy(['id' => $data['userMutations']]) as $mutation) {
					$mutations[] = $mutation;
				}
			}

			$user->mutations->set($mutations);
		}
	}






	public function deleteHash(User $user, UserHash $hash): void
	{
		$this->orm->userHash->remove($hash);
		$this->orm->user->persistAndFlush($user);
	}


	public function getByEmail(string $email, Mutation $mutation): ?User
	{
		return $this->orm->user->getByEmail($email, $mutation);
	}


	public function create(Mutation $mutation, string $email): User
	{

		$newUser = new User();
		$newUser->mutations->add($mutation);
		$newUser->email = $email;
		$newUser->password = '';
		$newUser->priceLevel = $this->orm->priceLevel->getDefault();
		$this->orm->user->attach($newUser);

		$this->orm->persistAndFlush($newUser);

		return $newUser;
	}

}
