<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Order\Delivery\DeliveryInfo;
use App\Model\Orm\Order\Delivery\DeliveryInformation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Order;
use Nette\Utils\ArrayHash;

interface DeliveryMethod
{
	public function getUniqueIdentifier(): string;

	public function getDeliveryType(): DeliveryType;

	public function supportsTracking(): bool;

	public function getTrackingUrl(string $trackingCode): string;

	public function isAvailableForOrder(Order $order): bool;

	public function onOrderPlaced(Order $order): void;

	public function onOrderCanceled(Order $order): void;

	public function useDeliveryAddress(): bool;

	public function processDeliveryDetails(ArrayHash $details): ArrayHash;
}
