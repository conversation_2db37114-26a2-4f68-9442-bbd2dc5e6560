<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Voucher;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\VoucherCode\VoucherCode;
use Brick\Money\Money;

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$vouchers}
 * @property VoucherCode $voucherCode {m:1 VoucherCode::$orderItems}
 */
final class VoucherItem extends OrderItem
{
	private function __construct()
	{
		parent::__construct();
	}

	public function getName(): string
	{
		return $this->voucherCode->getName();
	}

	public static function create(Order $order, VoucherCode $voucherCode): VoucherItem
	{
		$item = new self();
		$item->order = $order;
		$item->voucherCode = $voucherCode;
		$item->amount = 1;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->voucherCode->getMaxAmount($this->order);
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->voucherCode->getDiscount($this->order);
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->voucherCode->voucher->getVatRate($this->order->country);
	}
}
