<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use Nextras\Orm\Repository\Repository;
use function array_map;

final class DeliveryInformationRepository extends Repository
{
	public static function getEntityClassNames(): array
	{
		$types = DeliveryType::cases();

		return [
			DeliveryInformation::class,
			...array_map(
				static fn(DeliveryType $type) => $type->getEntityClassName(),
				$types,
			),
		];
	}

	public function getEntityClassName(array $data): string
	{
		$type = DeliveryType::from($data['type']);

		if ( ! $type instanceof DeliveryType) {
			throw new \LogicException('Cannot resolve DeliveryInformation type.');
		}

		return $type->getEntityClassName();
	}
}
