<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

enum DeliveryType: string
{
	case Physical = 'Physical';
	case Pickup = 'Pickup';
	case Store = 'Store';

	/**
	 * @return class-string<DeliveryInformation>
	 */
	public function getEntityClassName(): string
	{
		return match ($this) {
			self::Physical => PhysicalDeliveryInformation::class,
			self::Pickup => PickupDeliveryInformation::class,
			self::Store => StoreDeliveryInformation::class,
		};
	}
}
