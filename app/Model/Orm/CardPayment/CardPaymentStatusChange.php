<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment;

use App\Model\Orm\BackedEnumWrapper;
use DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 * @property-read CardPayment $payment {m:1 CardPayment::$statusChanges}
 *
 * @property-read DateTimeImmutable $changedAt
 * @property-read CardPaymentStatus|null $from {wrapper BackedEnumWrapper}
 * @property-read CardPaymentStatus $to {wrapper BackedEnumWrapper}
 */
final class CardPaymentStatusChange extends Entity
{
	public static function of(
		CardPayment $payment,
		CardPaymentStatus|null $from,
		CardPaymentStatus $to,
	): self
	{
		$self = new self();
		$self->setReadOnlyValue('payment', $payment);
		$self->setReadOnlyValue('changedAt', new DateTimeImmutable());
		$self->setReadOnlyValue('from', $from);
		$self->setReadOnlyValue('to', $to);
		return $self;
	}
}
