<?php declare(strict_types = 1);

namespace App\Model\Orm\ParameterValue;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ParameterValueMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'parameter_value';

	public function updateParameterSort(Parameter $parameter): Result
	{
		return $this->connection->query('UPDATE parameter_value SET parameterSort = %i WHERE parameterId = %i', $parameter->sort, $parameter->id);
	}

	public function findByAliasAndParameterAlias(string $parameterValueAlias, string $parameterValue): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->tableName, 'pv')
			->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
			->andWhere('pv.alias = %s', $parameterValueAlias)
			->andWhere('p.uid = %s', $parameterValue);

		return $this->toCollection($builder);
	}

	public function searchByInternalValue(string $search): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalValue  LIKE %_like_', $search);

		return $this->toCollection($builder);
	}

	public function findIdsForProductAllParameters(Product $product, array $parameterIds): array
	{
		$result = $this->connection->query(
			'SELECT * from product_parameter where productId = %i and parameterId in %i[]',
			$product->id,
			$parameterIds
		);

		return $result->fetchPairs('parameterValueId', 'parameterId');
	}

	public function findValuesByIdsOrderNumeric(Parameter $parameter, array $valueIds): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->tableName, 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy("pv.prioritySort DESC, CAST(REPLACE(pv.internalValue, ',', '.') AS DECIMAL(18,4)) ASC");

		return $this->toCollection($builder);
	}

	public function findRowsByParameterAndIds(Parameter $parameter, array $valueIds): Result
	{
		$builder = $this->builder()->select('pv.*')->from($this->tableName, 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy('pv.sort ASC');

		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findRowsByIds(array $valueIds): Result
	{
		$builder = $this->builder()->select('pv.*, p.uid as parameterUid')->from($this->tableName, 'pv');
		$builder->joinLeft('[parameter] AS [p]', '[p.id] = [pv.parameterId]');
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy('pv.parameterSort ASC, pv.sort');

		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findRowsByParameter(Parameter $parameter): Result
	{
		$builder = $this->builder()->select('pv.*')->from($this->tableName, 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);
		$builder->orderBy('pv.sort ASC');

		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->from($this->tableName, $this->tableName)
			->andWhere('%table.id in %i[]', $this->tableName, $ids)
			->orderBy('%raw', 'FIELD(' . $this->tableName . '.id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	/**
	 * @return ICollection|ParameterValue[]
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->from($this->tableName, $this->tableName)
			->andWhere('internalValue LIKE %_like_', $q);

		if (count($excluded) > 0) {
			$builder->andWhere('%table.id not in %i[]', $this->tableName, $excluded);
		}

		return $this->toCollection($builder);
	}

}
