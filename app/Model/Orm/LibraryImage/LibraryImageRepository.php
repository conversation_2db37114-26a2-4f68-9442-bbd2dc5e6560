<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Orm\CollectionById;
use App\Model\Orm\LibraryTree\LibraryTree;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method LibraryImage getById($id)
 * @method LibraryImage[]|ICollection findByFilter(array $filterData, ?LibraryTree $libraryTree)
 * @method LibraryImage[]|ICollection findByExactOrder(array $ids)
 * @method void fixSortForDirectory(LibraryTree $libraryTree)
 */
final class LibraryImageRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [LibraryImage::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
