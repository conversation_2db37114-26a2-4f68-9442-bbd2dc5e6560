<?php

declare(strict_types=1);

namespace App\Model\Orm\Product;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Traits\HasPublicParameter;
use App\Model\Orm\Traits\HasSimpleSave;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Product|null getById($id)
 * @method ICollection|Product[] findByPageId($id)
 * @method ICollection|Product[] findPromoted()
 * @method ICollection|Product[] findByFilter($filter)
 * @method ICollection|Product[] findRandom()
 * @method ICollection|Product[] findBySetpartsById($id)
 * @method ICollection|Product[] findSetBySetpartId($id)
 * @method ICollection|Product[] addToSet($set, $product, $sort)
 * @method ICollection|Product[] removeFromSet($set, $product)
 *
 * @method ICollection|Product[] searchByName($q, array $excluded = NULL, Mutation $mutation = null)
 *
 * @method ICollection|Tree[] findMainProductsInRelations(Product $attachedProduct, string $type)
 * @method ICollection|Tree[] findAttachedProductsInRelations(Product $mainProduct, string $type)
 *
 * @method ICollection|Product[] findProductsInTreeProductRelations(Tree $tree, string $type)
 * @method ICollection|Product[] addToPages($page, $product, $sort)
 * @method ICollection|Product[] removeFromPages($page, $product)
 * @method ICollection|Product[] updateToPages($page, $product, $sort)
 *
 * @method Result addParameterValue(Product $product, ParameterValue $parameterValue)
 * @method Result removeParameterValue(Product $product, ParameterValue $parameterValue)
 * @method Result removeParameter(Product $product, Parameter $parameter)
 * @method Result removeMissingParameterValuesIds(Product $product, array $selectedParameterValuesIds)
 *
 * @method ICollection|Product[] findSimilarProducts(Product $product, int $count, array $excludedIds, array $parameterUIDs)
 * @method ICollection|Product[] findFilteredProducts($productIds)
 * @method ICollection|Product[] findBestseller(Mutation $mutations, Tree $category = null)
 * @method Product save(?Product $entity, array $data)
 *
 * @method void cloneProductParameters(Product $sourceProduct, Product $targetProduct)
 *
 * @method Product[]|ICollection findByExactOrder(array $ids)
 * @method array findAllIds(?int $limit)

 */
final class ProductRepository extends Repository  implements CollectionById
{

	use HasPublicParameter;
	use HasSimpleSave;

	public static function getEntityClassNames(): array
	{
		return [Product::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		$ret = [
			'productLocalizations->public' => 1,
			'productLocalizations->mutation' => $orm->getMutation(),
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}

	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?Product
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}


}
