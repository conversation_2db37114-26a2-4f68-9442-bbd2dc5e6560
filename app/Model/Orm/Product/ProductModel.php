<?php declare(strict_types = 1);

namespace App\Model\Orm\Product;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariantModel;

final class ProductModel
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly Facade $productElasticFacade,
		private readonly \App\Model\ElasticSearch\All\Facade $allElasticFacade,
		private readonly ProductVariantModel $productVariantModel,
		private readonly MutationRepository $mutationRepository,
	)
	{
	}


	public function remove(Product $product): void
	{
		$this->productRepository->removeAndFlush($product);
	}


	public function saveToEs(Product $product): void
	{
		$this->allElasticFacade->updateNow($product);
		$this->productElasticFacade->updateAllMutationsNow($product);
	}

	/**
	 * @return bool - should return true, if the product flags were changed, and false otherwise
	 */
	public function handleFlags(Product $product): bool
	{
		$product->flushParam();
		foreach ($product->variants as $variant) {
			$variant->isInDiscount = 0; // @todo jk price akce (int)($variant->priceFinalDPH !=  $variant->priceDPH);
			//$variant->isFreeTransport = $product->isFreeTransport;
		}

		return true;
	}

	public function create(string $template): Product
	{
		$product = new Product();

		$product->template = $template;
		$this->productRepository->attach($product);
		$this->productRepository->persistAndFlush($product);

		$this->normalizeProduct($product);

		return $product;
	}

	public function normalizeProduct(Product $product): void
	{
		$mutations = $this->mutationRepository->findAll();

		if ( ! $product->variants->count()) {
			$variantShell = $this->productVariantModel->createEmpty();
			$product->variants->set([$variantShell]);
			$this->productRepository->persistAndFlush($product);
		}

		foreach ($mutations as $mutation) {

			$productLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
			if ($productLocalization === null) {
				$newProductLocalization = new ProductLocalization();
				$newProductLocalization->mutation = $mutation;
				$newProductLocalization->product = $product;
			}

			foreach ($product->variants as $variant) {
				$this->productVariantModel->normalizeVariant($variant, $mutation);
			}
		}

		$this->productRepository->persistAndFlush($product);
	}

}
