<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 *
 * @property PaymentMethodConfiguration $paymentMethod {m:1 PaymentMethodConfiguration::$prices}
 *
 * @property PriceLevel $priceLevel {m:1 PriceLevel, oneSided=true}
 * @property State $state {m:1 State, oneSided=true}
 *
 * @property Price $price {embeddable}
 */
final class PaymentMethodPrice extends Entity
{
}
