<?php

declare(strict_types=1);

namespace App\Model\CustomField;

use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\IEntity;

final class CustomFields
{
	/**
	 * @param array<string, CustomField[]> $templateMapping
	 */
	public function __construct(
		private array $templateMapping,
		private Orm $orm,
		private LazyValueFactory $lazyValueFactory,
	) {}

	public function prepareDataToSave(string $data): mixed
	{
        if ($data === '') {
            return new ArrayHash();
        }

		return Json::decode($data);
	}

	public function prepareForToShow(IEntity $object, \stdClass $data, bool $inRs = false): \stdClass
	{
		$result = new \stdClass();
		$customFields = $this->resolveCustomFieldsFor($object);
		// @phpstan-ignore-next-line
		foreach ($data as $name => $value) {
			if ( ! isset($customFields[$name])) {
				continue;
			}

			$customField = $customFields[$name];
			$fieldResult = $customField->process(
				$value,
				$inRs,
				$this->orm,
				$this->lazyValueFactory,
			);

			if ($fieldResult !== null) {
				$result->$name = $fieldResult;
			}
		}

		return $result;
	}

	public function prepareForToShowInRs(IEntity $object, \stdClass $data): \stdClass
	{
		return $this->prepareForToShow($object, $data, inRs: true);
	}

	/**
	 * @return array<string, CustomField>
	 */
	public function resolveCustomFieldsFor(IEntity $object): array
	{
		$customFields = [];

		$className = \get_class($object);
		$pos = \strrpos($className, '\\');
		if ($pos !== false) {
			$cleanClassName = Strings::firstLower(\substr($className, $pos + 1));
		} else {
			$cleanClassName = Strings::firstLower($className);
		}

		$objectTemplate = null;
		if (isset($this->templateMapping[$cleanClassName])) {
			$objectTemplate = $cleanClassName;
		} elseif ($object instanceof User) {
			if (isset($this->templateMapping['user-' . $object->role])) {
				$objectTemplate = 'user-' . $object->role;
			} else {
				$objectTemplate = 'users';
			}
		} elseif ($object instanceof Parameter) {
			if (isset($this->templateMapping['parameter-' . $object->uid])) {
				$objectTemplate = 'parameter-' . $object->uid;
			} else {
				$objectTemplate = 'parameters';
			}
		} elseif ($object instanceof ParameterValue) {
			if (isset($this->templateMapping['parameterValue-' . $object->internalAlias])) {
				$objectTemplate = 'parameterValue-' . $object->internalAlias;
			} else if (isset($this->templateMapping['parameterValue-' . $object->parameter->uid])) {
				$objectTemplate = 'parameterValue-' . $object->parameter->uid;
			} else {
				$objectTemplate = 'parameterValue';
			}
		} elseif ($object instanceof ProductLocalization) {
			$objectTemplate = $object->product->template;
		} elseif (isset($object->template)) {
			$objectTemplate = $object->template;
		}

		if ($objectTemplate !== null && isset($this->templateMapping[$objectTemplate])) {
			$customFields = \array_merge($customFields, $this->templateMapping[$objectTemplate]);
		}

		if (isset($object->uid) && $object->uid) {
			$uidKey = 'uid-' . $object->uid;
			if (isset($this->templateMapping[$uidKey])) {
				$customFields = \array_merge($customFields, $this->templateMapping[$uidKey]);
			}
		}

		$result = [];
		foreach ($customFields as $customField) {
			$result[$customField->name] = $customField;
		}

		return $result;
	}
}
