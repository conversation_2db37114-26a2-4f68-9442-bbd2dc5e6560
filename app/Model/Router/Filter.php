<?php declare(strict_types = 1);

namespace App\Model\Router;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nextras\Orm\Entity\IEntity;
use function array_map;
use function get_class;
use function serialize;
use function sprintf;

final class Filter
{

	private readonly Cache $cache;

	public function __construct(
		private readonly FilterLang $filterLang,
		private readonly FilterSeoLink $filterSeoLink,
		private readonly FilterAlias $filterAlias,
		private readonly FilterCommonParameters $filterCommonParameters,
		private readonly FilterFilterParams $filterFilterParams,
		private readonly FilterVariantId $filterVariantId,
		Storage $cacheStorage,
	)
	{
		$this->cache = new Cache($cacheStorage, self::class);
	}

	public function in(array $params): array
	{
		$params = $this->filterCommonParameters->in($params);
		$params = $this->filterLang->in($params);

		if (!isset($params['mutation'])) {
			// terminate request
			$params['presenter'] = false;
			return $params;
		}

		// map alias, lang => Presenter:action, idref/object
		$params = $this->filterAlias->in($params);
		if (!$params['presenter']) {
			$params = $this->filterAlias->in($params, true);
		}

		$params = $this->filterFilterParams->in($params);
		$params = $this->filterSeoLink->in($params);

		if (!$params['presenter']) {
			// terminate request
			return $params;
		}

		$params = $this->filterVariantId->in($params);

		return $params;
	}

	public function out(array $params): array
	{
		if (isset($params['request'])) {
			return $this->getOutParams($params);

		} else {
			$cacheKey = serialize(
				array_map(
					fn(mixed $param): mixed => $param instanceof IEntity && isset($param->id)
						? sprintf('%s:%s', get_class($param), $param->id)
						: $param,
					$params,
				),
			);

			return $this->cache->load($cacheKey, function (&$dependencies) use ($params): array {
				$dependencies[Cache::Expire] = '1 hour';
				return $this->getOutParams($params);
			});
		}
	}


	private function getOutParams(array $params): array
	{
		$params = $this->filterLang->out($params);
		$params = $this->filterVariantId->out($params);
		$params = $this->filterSeoLink->out($params);
		$params = $this->filterFilterParams->out($params);
		$params = $this->filterCommonParameters->out($params);
		return $params;
	}

}
