<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\PostType\Page\Model\Orm\CatalogTree;

final class FilterFilterParams
{

	private ?array $parameterAliasTable = null;

	public function __construct(
		private readonly ParameterRepository $parameterRepository,
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{}

	public function in(array $params): array
	{
		if (isset($params['object']) && $params['object'] instanceof CatalogTree) {
			$this->fillPossibleParams();
			// prevod parametru v aliasu
			if (preg_match('/(f_(.*)_f)/', $params['alias'], $matches)) {

				$params['alias'] = str_replace('/' . $matches[1], '', $params['alias']);
				$matches[2] = urldecode($matches[2]);

				$tmpParams = explode('_', $matches[2]);

				foreach ($tmpParams as $tmpParam) {
					if (preg_match('/^(.*)\.(.*)$/U', $tmpParam, $selectMatches)) {

						// jedna se o SELECT/MULTISELECT

						$selectValuesTmp = explode(',', $selectMatches[2]);
						foreach ($selectValuesTmp as $selectValue) {
							$params[$selectMatches[1] . '_' . $selectValue] = 1;
						}
					} else {
						$params[$tmpParam] = 1;
						// jedna se o FLAG
					}
				}
			}

			foreach ($params as $name => $value) {
				if (in_array($name, ['alias', 'urlPrefix'], true)) {
					continue;
				}

				if (preg_match('/r_([a-zA-Z0-9\-]+)_(min|max)/', $name, $matches)) {
					unset($params[$name]);
					$params['filter']['ranges'][$matches[1]][$matches[2]] = $value;
				} elseif (preg_match('/([a-zA-Z0-9\-]+)_([a-zA-Z0-9\-]+)/', $name, $matches)) {
					// pravdepodobne se jedna o multiselect/select
					$parameterAlias = $matches[1];
					$parameterValueAlias = $matches[2];
					if (isset($this->parameterAliasTable[$name])) {
						unset($params[$name]);
						if (!isset($params['filter']['dials'][$parameterAlias])) {
							$params['filter']['dials'][$parameterAlias] = [];
						}

						$params['filter']['dials'][$parameterAlias][$this->parameterAliasTable[$name]] = $this->parameterAliasTable[$name];
					}
				} else {
					// je to flag
//					if (in_array($name, $this->filterSetup['flagValues'], true) ||
//						in_array($name, $this->filterSetup['flags'], true)
//					) {
//						unset($params[$name]);
//						if ($value !== null) {
//							$params['filter']['flags'][$name] = $value;
//						}
//					}
				}
			}
		}

		return $params;
	}

	public function out(array $params): array
	{
		$aliasSufix = [];

		if (isset($params['filter']) && isset($params['object']) && $params['object'] instanceof CatalogTree) {

			$object = $params['object'];
			if (isset($params['filterSubmit'])) {
				// filterSubmit is name of submit action on filter
				// if is present this wil enforce redirect
				unset($params['filterSubmit']);
			}

			if (isset($params['filter']['ranges'])) {
				foreach ($params['filter']['ranges'] as $name => $values) {
					if (isset($values['max']) && $values['max']) {
						$params['r_' . $name . '_max'] = $values['max'];
					}

					if (isset($values['min']) && $values['min']) {
						$params['r_' . $name . '_min'] = $values['min'];
					}
				}
			}


			if (isset($params['filter']['dials'])) {
				foreach ($params['filter']['dials'] as $name => $valuesIds) {
					$parametersOptions = $this->parameterValueRepository->findRowsByIds($valuesIds);
					if ($object->isImportantParameter($name)) {
						$aliases = [];
						foreach ($parametersOptions as $option) {
							$aliases[] = $option->internalAlias;
						}

						if (count($aliases) > 0) {
							$aliasSufix[] = $name . '.' . implode(',', $aliases);
						}
					} else {
						foreach ($parametersOptions as $option) {
							$params[$option->parameterUid . '_' . $option->internalAlias] = 1;
						}
					}
				}
			}

			unset($params['filter']['dials']);
			unset($params['filter']['ranges']);
		}

		ksort($params);

		if ($aliasSufix !== []) {
			$params['alias'] .= '/f_' . implode('_', $aliasSufix) . '_f';
		}

		return $params;
	}

	private function fillPossibleParams(): void
	{
		if (!$this->parameterAliasTable) {
			$this->parameterAliasTable = [];

			$possibleDialParameters = $this->parameterRepository->findBy(['isInFilter' => 1])->orderBy('sort');
			foreach ($possibleDialParameters as $parameter) {
				$options = $this->parameterValueRepository->findRowsByParameter($parameter);
				foreach ($options as $option) {
					$this->parameterAliasTable[$parameter->uid . '_' . $option->internalAlias] = $option->id;
				}
			}
		}
	}

}
