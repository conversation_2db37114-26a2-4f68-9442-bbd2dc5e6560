<?php declare(strict_types=1);

namespace App\Console\Elastic;

use App\Model\Orm\Orm;
use Exception;
use App\Model\ElasticSearch\IndexModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'elastic:index:purge',
	description: 'Remove all elastic data from orm and elastic'
)]
final class PurgeCommand extends Command
{
	private string $description = 'To run this !!DANGEROUS!! command use parameter FORCE (-f)';


	public function __construct(
		private readonly IndexModel $indexModel,
		private readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->setName('elastic:index:purge')
			->addOption('force', 'f', InputOption::VALUE_NONE, $this->description);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$forced = $input->getOption('force');
		if ($forced) {

			$output->writeLn('REMOVE FROM ORM');

			foreach ($this->orm->esIndex->findAll() as $esIndex) {
				$output->writeLn($esIndex->esName);
				$this->orm->removeAndFlush($esIndex);
			}

			$output->writeLn('REMOVE FROM ES');

			foreach ($this->indexModel->findElasticSearchCreatedIndexes() as $remoteIndex) {
				$output->writeLn($remoteIndex['index']);
				$this->indexModel->getIndexByName($remoteIndex['index'])->delete();
			}
			$output->writeLn('DONE');
			return self::SUCCESS;
		} else {
			throw new Exception($this->description);
		}
	}
}

