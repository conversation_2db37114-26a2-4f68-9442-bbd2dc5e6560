<?php declare(strict_types=1);

namespace App\Console\Elastic;

use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\Orm;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'elastic:index:clean',
	description: 'Remove all unused indexes',
)]
final class CleanCommand extends Command
{

	public function __construct(
		private readonly EsIndexFacade $esIndexFacade,
		private readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$output->writeLn('REMOVE FROM ORM');

		foreach ($this->orm->esIndex->findBy([
			'active' => 0
		]) as $esIndex) {
			$output->writeLn($esIndex->name);
			$this->esIndexFacade->delete($esIndex);
		}

		$output->writeLn('DONE');
		return self::SUCCESS;
	}

}

