<?php

declare(strict_types=1);

namespace App\Infrastructure\Debugger;

use Nette\StaticClass;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Entity\ToArrayConverter;
use Nextras\Orm\Relationships\IRelationshipCollection;
use <PERSON>\Debugger;
use <PERSON>\Dumper\Describer;
use Tracy\Dumper\Value;

final class OrmExposer
{

	use StaticClass;

	public static function exposeEntity(
		IEntity $entity,
		Value $value,
		Describer $describer,
	): void
	{
		if ($value->depth > 2) {
			return;
		}

		$data = ToArrayConverter::toArray($entity);
		foreach ($data as $name => $item) {
			$describer->addPropertyTo($value, (string) $name, $item);
		}
	}

	public static function exposeCollection(
		ICollection $collection,
		Value $value,
		Describer $describer,
	): void
	{

		if ($value->depth > Debugger::$maxDepth) {
			return;
		}

		$rows = $collection->fetchAll();
		foreach ($rows as $entityKey => $entity) {
			$describer->addPropertyTo($value, (string) $entityKey, $entity);
		}
	}

	public static function exposeRelationshipCollection(
		IRelationshipCollection $collection,
		Value $value,
		Describer $describer,
	): void
	{
		if ($value->depth > Debugger::$maxDepth) {
			return;
		}

		foreach ($collection as $entityKey => $entity) {
			$describer->addPropertyTo($value, (string) $entityKey, $entity);
		}
	}

}
