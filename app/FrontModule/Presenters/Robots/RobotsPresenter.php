<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Robots;

use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Robots\RobotsRows;
use Nette\Application\UI\Presenter;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\Response;

final class RobotsPresenter extends Presenter
{

	public $autoCanonicalize = false;

	private Mutation $mutation;

	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly MutationDetector $mutationDetector,
		private readonly Response $response,
		private readonly RobotsRows $robotsRows,
		private readonly Storage $storage,
		private readonly Orm $orm,
	)
	{
	}


	protected function startup()
	{
		parent::startup();
		$this->mutation = $this->mutationDetector->detect();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
	}

	public function renderDefault(): void
	{
		$cache = new Cache($this->storage, 'robots');
		$this->response->setContentType('text/plain');

		$this->template->rows = $cache->load($this->mutation->langCode, function (&$dependencies) {
			$dependencies[Cache::Expire] = '30 minutes';
			return $this->robotsRows->get($this->mutation);
		});

		$this->template->setFile(__DIR__ . '/templates/' . $this->view . '.latte');
	}

}
