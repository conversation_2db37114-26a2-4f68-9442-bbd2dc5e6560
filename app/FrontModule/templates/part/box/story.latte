{default $class = 'u-mb-4xl'}
{default $cf = $object->cf->story ?? false}

<article n:if="$cf" n:class="b-story, $class">
	<div class="row-main">
		<h2 n:if="$cf->title ?? false" class="b-story__title pp-18 u-title-line">{$cf->title}</h2>
		<div class="grid">
			<div class="grid__cell size--6-12@sm">
				{include $templates.'/part/box/content.latte', class: 'b-story__content1', content: $cf->content1 ?? false}
			</div>
			<div class="grid__cell size--6-12@sm">
				<p class="b-story__img u-mb-lg">
					{php $img = isset($cf->img) ? $cf->img->getEntity() ?? false : false}
					{if $img}
						<img n:if="$img" class="img" src="{$img->getSize('lg')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
						{else}
						<img class="img" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
					{/if}
				</p>
				{include $templates.'/part/box/content.latte', class: 'b-story__content2', content: $cf->content2 ?? false}
			</div>
		</div>
	</div>
</article>