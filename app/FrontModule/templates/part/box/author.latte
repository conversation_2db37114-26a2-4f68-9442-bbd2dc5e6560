{default $class = false}
{default $person = false}

<article n:if="$person" b:class="b-author, $class, link-mask">
	<div class="b-author__img">
		<div class="img">
			{if $person->getFirstImage() !== null}
				{php $img = $person->getFirstImage()->getSize('md')}
				<img src="{$img->src}" alt="{$person->name}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}
		</div>
	</div>
	<div class="b-author__content u-pt-sm u-mb-last-0">
		<h3 class="b-author__title">
			<a n:href="$person" class="b-author__link link-mask__link">
				{$person->name}
			</a>
			<span class="b-author__value">
				({$person->blogsPublic->count()})
			</span>
		</h3>
		<p n:if="$person->cf->base?->position ?? false" class="b-author__desc">
			{$person->cf->base->position}
		</p>
		<p n:if="$person->cf->base?->annotation ?? false" class="b-author__annot">
			{$person->cf->base->annotation}
		</p>
	</div>
</article>
