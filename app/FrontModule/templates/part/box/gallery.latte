{default $class = 'u-mb-lg'}
{default $images = []}

<div n:if="count($images)" n:class="b-gallery, $class">
	<div class="embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<ul class="grid grid--scroll embla__container">
				<li n:foreach="$images as $image" class="grid__cell size--3-12">
					<a href="{$image->getSize('2xl')->src}" class="img img--16-9" data-modal='{"gallery": "cc"}'>
						<img src="{$image->getSize('md')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
					</a>
				</li>
			</ul>

			<div class="embla__dots is-disabled" data-embla-target="dots"></div>
			<script type="text/template" class="embla-dot-template">
				<button class="embla__dot" type="button">%i</button>
			</script>

			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('test-chevron-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('test-chevron-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
	</div>
</div>