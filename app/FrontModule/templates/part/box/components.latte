{default $cf = $object->cf->components ?? false}
{default $class = 'u-mb-2xl'}

<section n:if="$cf" n:class="b-components, $class">
	<div class="row-main b-components__wrap">
		<div class="b-components__table-wrapper">
			<table class="b-components__table">
				<thead>
					<tr>
						<th colspan="2">
							<h2 n:if="$cf->title ?? false" class="b-components__title h3">{$cf->title}</h2>
						</th>
						<th>
							<p n:if="$cf->description ?? false" class="b-components__text u-mb-l">{$cf->description}</p>
						</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
				{foreach $cf->items as $item}
					<tr class="b-components__row">
						<td class="b-components__label">
							{if $item->image ?? false}
								{capture $tooltipHtml}
									<img src="{$item->image->getSize('sm')->src}"
										 alt="{$item->label|noescape}" class="b-components__hover-img" width="160" height="219">
								{/capture}
								<span class="b-components__hover"
									  data-controller="tooltip"
									  data-tippy-theme="transparent"
									  data-tippy-content="{$tooltipHtml|noescape}"></span>
							{/if}
							<span>{$item->label|noescape}</span>
						</td>
						<td class="b-components__name">{$item->name}</td>
						<td class="b-components__desc">{$item->description}</td>
						<td>
							{if $item->link}
								<a class="b-components__link" href="{$item->link}" target="_blank" rel="noopener">
									{$item->linkLabel ?: $item->link}
								</a>
							{/if}
						</td>
					</tr>
				{/foreach}
				</tbody>
			</table>
		</div>
	</div>
</section>
