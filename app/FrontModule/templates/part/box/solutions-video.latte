{default $class = ''}
{default $cf = $object->cf->solutions_video ?? false}

<div n:if="$cf" n:class="b-solutions-video, $class, u-limit-w">
	{php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
	{include $templates.'/part/core/video.latte', class: 'b-solutions-video__video', link: $cf->video ?? false, hidePlayBtn: true, poster: $img, posterSize: 'max-16-9', imgClass: 'b-solutions-video__video-img'}
	<div n:if="$cf->title ?? false" class="b-solutions-video__content">
		<div class="row-main">
			<h2 class="u-mb-0">{$cf->title|texy|noescape}</h2>
		</div>
	</div>
</div>