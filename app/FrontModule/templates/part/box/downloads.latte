{default $cf = $object->cf->downloads ?? false}
{default $class = 'u-mb-2xl'}

<section n:if="$cf" n:class="b-downloads, $class">
	<div class="row-main b-downloads__wrap">
		<div class="b-downloads__list">
			{foreach $cf->branches as $branch}
				<section class="b-downloads__section">
					<h2 class="h3 b-downloads__section-title">{$branch->title}</h2>

					<div class="b-downloads__table-wrapper">
						<table class="b-downloads__table">
							{foreach $branch->items as $item}
								<tr>
									<td>
										<strong class="b-downloads__file-name">{$item->title}</strong>
									</td>
									<td>
										<span class="b-downloads__file-meta">
											{$item->file->ext|upper} ({$item->file->size|bytes})
										</span>
									</td>
									<td>
										<button type="button"
											class="b-downloads__link as-link"
											data-controller="clipboard"
											data-clipboard-text-value="{$baseUrl . $item->file->url}"
											data-tippy-content="{_cf_downloads_clipboarded}"
											data-action="click->clipboard#copy"
										>
											{_cf_downloads_share}
  											{('link')|icon}
										</button>
									</td>
									<td>
										<a href="{$item->file->url}" class="b-downloads__link" download>
											{_cf_downloads_download}
											{('download')|icon}
										</a>
									</td>
								</tr>
							{/foreach}
						</table>
					</div>
				</section>
			{/foreach}
		</div>
	</div>
</section>
