{default $class = 'u-pb-lg'}
{default $cf = $object->cf->career ?? false}

<div n:if="$cf" n:class="b-lab, $class, u-ta-c" data-controller="parallax">
	<div class="row-main row-main--full">
		<div class="b-lab__inner">
			{php $bg = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
			{if $bg}
				<img class="f-join__bg" srcset="
						{$bg->getSize('lg-16-9')->src} 750w,
						{$bg->getSize('xl-16-9')->src} 1200w,
						{$bg->getSize('2xl-16-9')->src} 1920w,
						{$bg->getSize('max-16-9')->src} 3840w"
					sizes="(max-width: 2560px) 100vw, 2560px"
					src="{$bg->getSize('max-16-9')->src}"
					alt="{$bg->getAlt($mutation)}" fetchpriority="high" loading="eager"
					data-parallax-target="bg">
			{else}
				<img class="b-lab__bg" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}

			<div class="row-main row-main--full" data-parallax-target="content">
				<div class="b-lab__content u-maw-8-12 u-mx-auto u-mb-last-0">
					<h2 n:if="$cf->title1 ?? false" class="pp-18 u-mb-md">{$cf->title1}</h2>
					<p n:if="$cf->title2 ?? false" class="pp-48 u-mb-xl">{$cf->title2|texy|noescape}</p>
					<p n:if="isset($pages->career) && ($cf->btnText ?? false)">
						<a href="{plink $pages->career}" class="btn btn--inverse btn--bd">
							<span class="btn__text">
								{$cf->btnText}
							</span>
						</a>
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
