{default $class = false}
{default $url = false}
{default $alt = false}
{default $lazyLoading = true}
{default $width = '600'}

{if strpos($url, 'youtube')}
	{php $urlObject =  new \Nette\Http\Url($url)}
	{php $id = $urlObject->getQueryParameter('v')}
	{php $thumbUrl = "https://i3.ytimg.com/vi/{$id}/maxresdefault.jpg"}
	{php $headers = @get_headers($thumbUrl)}

	{if strpos($headers[0],'200')===false} {* neeesixtuje *}
		{php $imageName = 'hqdefault'}
	{else} {* existuje *}
		{php $imageName = 'maxresdefault'}
	{/if}

	<img n:class="$class" src="https://images.weserv.nl/?url=https://i3.ytimg.com/vi/{$id}/{$imageName}.jpg&w={$width}" alt="{$alt}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
{elseif strpos($url, 'vimeo')}
	{var $id = pathinfo(explode('/', $url)[count(explode('/', $url))-1], PATHINFO_FILENAME)}

	<img n:class="$class" src="https://images.weserv.nl/?url=https://vumbnail.com/{$id}.jpg&w={$width}" alt="{$alt}"{if $lazyLoading} loading="lazy"{else} fetchpriority="high"{/if}>
{/if}
