{varType App\Model\Orm\ProductLocalization\ProductLocalization $object}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

{*
{dump $object->isInStock} // bool skladem celkove
{dump $object->isInStockDefault} // bool je skladem na centralnim skladu
{dump $object->isInStockSupplier} // bool je skladem u dodavatele
{dump $object->suppliesByStock} // sklady podle ID
{dump $object->suppliesByStockAlias} // sklady podle aliasu
{dump $object->totalSupplyCount} // abs pocet na sklade celkem
{dump $object->suplyCountStockDefault} // abs pocet na defaultnim sklade
{dump $object->suplyCountStockSupplier} // abs pocet u dodavatele
*}

<p class="availability{if $product->isInStock && !$product->isOld && !$product->isInPrepare} availability--available{elseif !$product->isInPrepare} availability--unavailable{/if}">
	{if $product->isOld}
		<strong>
			{_'availability_unavailable'}
		</strong>
	{elseif $product->notSoldSeparately}
		<strong>
			{_'not_sold_separately'}
		</strong>
	{elseif $product->isInPrepare}
		<strong>
			{_'presale_start'}
		</strong>
	{elseif $product->isInStock}
		<strong>
			{_'availability_in_stock'}
		</strong><br>
		{translate}{$product->totalSupplyCount|stock}{/translate} ({$product->totalSupplyCount} {_'stock_piece'})<br>

		<small n:inner-foreach="$variant->suppliesByStock as $supply">
			{_'stock_name_alias_' . $supply->stock->alias}:
			<span n:class="$supply->amount == 0 ? u-color-red">
				{translate}{$supply->amount|stock}{/translate} ({$supply->amount} {_'stock_piece'})
			</span><br>
		</small>
	{else}
		<strong>
			{_'availability_soldout'}
		</strong>
	{/if}
</p>
