{default $class = false}
{varType App\Model\Orm\State\State $selectedCountry}
<p n:class="inp-country, $class" data-controller="country">
	<select n:if="$countrySelect" n:name=country class="inp-country__select" data-country-target="select" data-controller="autosubmit" data-action="change->country#change autosubmit#submitForm">
	</select>

	<span class="inp-country__box">
		<img class="inp-country__flag" src="/static/img/flags/{$selectedCountry->code|lower}.svg" alt="{_$selectedCountry->name}" data-country-target="flag">
		<span class="inp-country__name" data-country-target="name">
			{_$selectedCountry->name}
		</span>
	</span>
</p>
