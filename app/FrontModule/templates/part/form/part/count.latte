{default $class = false}
{default $amount = 1}
{default $maxAmount = 1000}
{default $variantId = $variant??->id ?? null}
{default $autosubmit = false}
{default $disableZero = false}

{*if isset($variant)}
	{var $maxAmount = $variant->totalSupplyCount}
{/if*}

{if $amount > $maxAmount}
	{var $amount = $maxAmount}
{/if}

{var $inputAmount = $input->getValue()}

<span n:if="$amount >= 0 && $variantId && maxAmount" n:class="inp-count, $class" data-controller="inp-count" data-inp-count-min-value="{if $disableZero}1{else}0{/if}" data-inp-count-max-value="{$maxAmount}">
	<label for="amount_{$variantId}" class="u-vhide">{_"amount"}</label>
	<button type="button" class="btn inp-count__tool inp-count__tool--minus{if $disableZero && $inputAmount <= 1} is-disabled{/if}" data-step="-1" data-action="inp-count#changeValue" data-inp-count-target="toolMinus">
		<span class="u-vhide">-</span>
	</button>
	<input n:name="$input" class="inp-count__inp inp-text" id="amount_{$variantId}" data-inp-count-target="inp" max="{$maxAmount}"{if $autosubmit} data-controller="autosubmit" data-action="input->inp-count#checkValue change->autosubmit#submitForm"{else} data-action="input->inp-count#checkValue"{/if}>
	<button type="button" class="btn inp-count__tool inp-count__tool--plus" data-step="1" data-action="inp-count#changeValue" data-inp-count-target="toolPlus">
		<span class="u-vhide">+</span>
	</button>
</span>
