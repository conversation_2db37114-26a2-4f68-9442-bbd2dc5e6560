<nav class="m-lang" data-controller="toggle-class etarget" data-etarget-prevent-default-value="true">
	<button class="m-lang__btn" data-action="toggle-class#toggle" aria-expanded="false">
		<img n:if="$selectedMutation->cf->mutationData->icon ?? false" class="m-lang__img" src="/static/img/flags/{$selectedMutation->cf->mutationData->icon|lower}.svg" alt="{$selectedMutation->langMenu}" width="16" height="10">
		{$selectedMutation->name} [{$selectedMutation->langCode}]
		{('test-chevron-down')|icon, 'm-lang__arrow'}
	</button>
	<ul class="m-lang__list">
        {foreach $mutations as $k => $mutation}
			<li class="m-lang__item{if $selectedMutation->id === $mutation->id} is-active{/if}">
				<a n:href="changeLanguage!, id => $mutation->id" class="m-lang__link">
					<img n:if="$mutation->cf->mutationData->icon ?? false" class="m-lang__img" src="/static/img/flags/{$mutation->cf->mutationData->icon|lower}.svg" alt="{$mutation->langMenu}" width="16" height="10">
					{$mutation->name} [{$mutation->langCode}]
				</a>
			</li>
		{/foreach}
	</ul>
</nav>
