{include '../../../inp.latte', form: $form, name: inv_firstname, required: true, validate: true}
{include '../../../inp.latte', form: $form, name: inv_lastname, required: true, validate: true}
{include '../../../inp.latte', form: $form, name: inv_phone, required: true, validate: true, type=>'tel'}
{include '../../../inp.latte', form: $form, name: inv_street, required: true}
{include '../../../inp.latte', form: $form, name: inv_city, required: true}

{if isset($form['inv_countrycode'])}
	{include '../../../inp.latte', form: $form, name: inv_countrycode, required: true, validate: true}
{/if}

<div class="grid grid--y-0 grid--bottom">
	<div class="grid__cell size--5-12@sm">
		{include '../../../inp.latte', form: $form, name: inv_zip, required: true}
	</div>
	<div n:class="grid__cell, 'size--7-12@sm'">
		<p class="inp-fix">
			<label name="inv_state" class="inp-label">
				{_"form_label_state"}:
			</label>
			<input type="text" name="inv_state" id="inv_state" class="inp-text" placeholder="{_$state->name}" disabled>
		</p>
	</div>
</div>

