{snippet form}

	{form form autocomplete: 'off', novalidate: "novalidate", class: 'f-address', autocomplete: 'off'}
		{control formMessage, $flashes, $form}

		{if $customAddress}
			{foreach $customAddress as $k=>$address}
				<div class="b-address u-mb-sm">
					{if empty($address->delCity)}
						<p>
							<strong class="u-d-b">{_'delivery_invoice_address_title'}</strong>
							{$address->invFirstname} {$address->invLastname}<br>
							{$address->invStreet}<br>
							{$address->invCity}<br>
							{$address->invZip}<br>
							{if $address->invPhone}{$address->invPhone}<br>{/if}
							{if $address->invCompany}{$address->invCompany}<br>{/if}
							{if $address->invIc}{_'form_label_ic'}: {$address->invIc}<br>{/if}
							{if $address->invDic}{_'form_label_dic'}: {$address->invDic}{/if}
						</p>
					{else}
						<div class="grid">
							<div class="grid__cell size--6-12@md">
								<p>
									<strong>
										{_'delivery_address_title'}
									</strong><br>

									{$address->delFirstname} {$address->delLastname}<br>
									{$address->delStreet}<br>
									{$address->delCity}<br>
									{$address->delZip}<br>
									{if $address->delPhone}{$address->delPhone}<br>{/if}
									{if $address->delCompany}{$address->delCompany}{/if}
								</p>
							</div>
							<div class="grid__cell size--6-12@md">
								<p>
									<strong>
										{_'invoice_address_title'}
									</strong><br>
									{$address->invFirstname} {$address->invLastname}<br>
									{$address->invStreet}<br>
									{$address->invCity}<br>
									{$address->invZip}<br>
									{if $address->invPhone}{$address->invPhone}<br>{/if}
									{if $address->invCompany}{$address->invCompany}<br>{/if}
									{if $address->invIc}{_'form_label_ic'}: {$address->invIc}<br>{/if}
									{if $address->invDic}{_'form_label_dic'}: {$address->invDic}{/if}
								</p>
							</div>
						</div>
					{/if}

					<p class="grid">
						<span class="grid__cell size--auto">
							<button type="button" class="as-link item-icon" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-class="is-open" data-toggle-content=".f-open--{$iterator->getCounter()}">
								{('test-pencil')|icon, 'item-icon__icon'}
								<span class="item-icon__text">
									{_'btn_change'}
								</span>
							</button>
						</span>
						<span class="grid__cell size--auto">
							{capture $confirmMsg}
								{if empty($address->delStreet)}
									{$address->invFirstname} {$address->invLastname}, {$address->invStreet}, {$address->invZip} {$address->invCity}
								{else}
									{$address->delFirstname} {$address->delLastname}, {$address->delStreet}, {$address->delZip} {$address->delCity}
								{/if}
							{/capture}

							{* {dump preg_replace('/\t/', '', $confirmMsg)} *}
							<a n:href="deleteCustomAddress!, key:  $k" class="item-icon">
								{('close')|icon, 'item-icon__icon'}
								<span class="item-icon__text">
									{_'btn_delete'}
								</span>
							</a>
						</span>
					</p>

					<div n:class="f-open, 'f-open--'.$iterator->getCounter()" data-controller="toggle-class">
						<div n:class="f-open__box, u-pt-sm"> {*\SuperKoderi\Components\OrderStep2Form::hasBoxError($form, $k)*}
							{include 'addressBox.latte', form: $form, nameSuffix: '_'.$k}
						</div>
					</div>
				</div>
			{/foreach}
		{/if}

		<div n:class="f-open, $form['ca_isNew']->value ? is-open" data-controller="toggle-class">
			<input n:name="ca_isNew" class="inp-item__inp" data-action="change->toggle-class#toggleInp" value="1">
			<div n:class="f-open__box">
				{include 'addressBox.latte', form: $form}
			</div>
			<div class="grid grid--space-between">
				<div class="grid__cell size--auto">
					<p class="f-open__btn u-mb-0">
						<button type="button" class="btn" data-action="toggle-class#toggle">
							<span class="btn__text">
								<span class="item-icon">
									<span class="item-icon__text">
										{_'add_address'}
									</span>
									{('test-chevron-down')|icon, 'item-icon__icon'}
								</span>
							</span>
						</button>
					</p>
				</div>
				<div class="grid__cell size--auto">
					<p class="u-mb-0">
						<button class="btn" n:name="save">
							<span class="btn__text">
								{_'btn_save'}
							</span>
						</button>
					</p>
				</div>
			</div>
		</div>
	{/form}
{/snippet}
