<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ChangePasswordForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\TranslatorDB;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use App\FrontModule\Components\HasError500Catcher;
use App\Exceptions\LogicException;
use App\Model\Mutation\MutationHolder;

final class ChangePasswordForm extends UI\Control
{
	use HasError500Catcher;

	public function __construct(
		private readonly Tree $object,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly MutationHolder $mutationHolder,
		private readonly \App\Model\Email\CommonFactory $commonEmailFactory,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
	) {}

	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);

			$this->template->object = $this->object;
			$this->template->userEntity = $this->user;

			$this->template->render(__DIR__ . '/changePasswordForm.latte');

		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$form->addHidden('id', $this->user->id);
		$form->addPassword('password', 'form_label_password')
			->setRequired('form_password_empty');
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired('form_password_empty')
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);

		$form->addSubmit('save', 'btnSave');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @throws LogicException
	 * @throws UI\InvalidLinkException
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = (array)$form->getHttpData();

		try {

			if ($this->user->id != $values['id']) {
				throw new LogicException('Unknown user' . $values['id']);
			}

			$user = $this->userModel->save($this->user, $valuesAll, $this->user->id);

			$valuesAll['email'] = $user->email;
			$valuesAll['name'] = $user->name;
			$valuesAll['link'] = $this->mutationHolder->getMutation()->getBaseUrlWithPrefix() . $this->presenter->link($this->mutationHolder->getMutation()->pages->lostPassword, ['email' => $user->email]);
			$this->commonEmailFactory
				->create()
				->send('', $user->email, 'passwordChanged', $valuesAll);


		} catch (\Exception $e) {
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$form->setDefaults([], true);
			$this->redrawControl();
		} else {
			$this->flashMessage('form_profile_password_changed', 'ok');
			$this->presenter->redirect('this');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
