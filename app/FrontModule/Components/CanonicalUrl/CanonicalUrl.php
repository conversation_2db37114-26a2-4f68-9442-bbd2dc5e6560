<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CanonicalUrl;

use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\TranslatorDB;

/**
 * @property-read DefaultTemplate $template
 */
final class CanonicalUrl extends UI\Control
{
	public function __construct(
		private TranslatorDB $translator,
	) {}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

//		$prev = null;
//		$next = null;
		$showCanonical = true;

//		$this->template->filter = $this->presenter->request->getParameter('filter');
		//$params = $this->presenter->getParameters();
//		if (isset($this->object->template)) {
//			if ($this->visualPaginator) {
//				$paginator = $this->visualPaginator->getPaginator();
////				if ($paginator->page > 1) {
////					$prev = $paginator->page - 1;
////				}
////
////				if ($paginator->page < $paginator->getLastPage()) {
////					$next = $paginator->page + 1;
////				}
//
//
//				if ($paginator->page > 1) {
//					$showCanonical = true;
//				}
//			}
//		}

		$this->template->showCanonical = $showCanonical;
//		$this->template->prev = $prev;
//		$this->template->next = $next;

		$this->template->render(__DIR__ . '/canonicalUrl.latte');
	}
}
