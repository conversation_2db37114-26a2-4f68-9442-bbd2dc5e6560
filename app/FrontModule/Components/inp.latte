{default $class = false}
{default $cols = false}
{default $rows = false}
{default $type = isset($form[$name]) ? $form[$name]->getOption('type') : false}
{default $label = isset($form[$name]) ? ($type == 'checkbox' ? $form[$name]->caption : $form[$name]->label->getText()) : ''}
{default $labelLang = false}
{default $inpClass = false}
{default $labelClass = false}
{default $labelReplace = false}
{default $agreeLabel = false}
{default $validate = false}
{default $noP = false}
{default $disabled = isset($form[$name]) ? $form[$name]->isDisabled() : false}
{default $required = isset($form[$name]) ? $form[$name]->isRequired() : false}
{default $placeholder = ''}
{default $placeholderLang = false}
{if is_string($placeholderLang)}{capture $placeholder}{translate}{$placeholderLang}{/translate}{/capture}{/if}
{default $dataAction = false}
{default $validate = false}
{default $showError = true}

{if $validate}{php $inpClass = $inpClass . ' inp-validate'}{/if}
{capture $label}{if $labelLang}{translate}{$labelLang}{/translate}{else}{$label}{/if}{if $label && $type != 'checkbox'}:{/if}{if $required && $label} <span class="inp-required">*</span>{/if}{/capture}

<p n:tag="$noP ? 'span'" n:class="$form[$name]->errors ? has-error, $class">
	{if $type == 'checkbox'}
		<label n:class="inp-item, inp-item--checkbox, $labelClass">
			<input n:name="{$name}" n:class="inp-item__inp, $validate ? inp-validate"{if $dataAction} data-action="{$dataAction}"{/if}>
			<span class="inp-item__text">
				{if $labelReplace}
					{$label|replace:"%link%",$labelReplace|noescape}
				{elseif $agreeLabel}
					{_form_label_agree_text} <a href="{plink $pages->personalData}">{_form_label_agree_link}</a> <span class="inp-required">*</span>
				{else}
					{$label|noescape}
				{/if}
			</span>
		</label>
	{elseif $type == 'radio'}
		<span n:class="inp-label, $labelClass">{$label|noescape}</span>

		<span class="inp-items">
			{foreach $form[$name]->items as $k=>$i}
				<label class="inp-items__item inp-item inp-item--radio">
					<input type="radio" name="{$name}" value="{$k}" n:class="$validate ? inp-validate" {if $form[$name]->value == $k} checked="checked"{/if}{if $dataAction} data-action="{$dataAction}"{/if}>
					<span class="inp-item__text">
						{translate}{$i|noescape}{/translate}
					</span>
				</label>
			{/foreach}
		</span>
	{else}
		{if $type == 'file'}
			<span class="inp-fix inp-fix--file">
				{input $name class=>'inp-file', data-text=>$dataText, data-action=>$dataAction}
				<label n:name="{$name}" id="fileInput" n:class="inp-label, $labelClass">{$label|noescape}</label>
				<button type="button" class="btn btn--bd" onclick="document.getElementById('fileInput').click()">
					<span class="btn__text">
						{capture $dataText}{_select_file}{/capture}
					</span>
				</button>
			</span>
		{elseif $type == 'select'}
			<span class="inp-fix inp-fix--select">
				{input $name class=>'inp-select', data-action=>$dataAction, aria-describedby=>$name.'_error'}
				<span id="{$name.'_error'}" n:if="$validate && $showError" class="inp-error"></span>
				<label n:name="{$name}" n:class="inp-label, $labelClass">{$label|noescape}</label>
			</span>
		{elseif $type == 'tel'}
			<span class="inp-fix" data-controller="inp-phone">
				{* phone preffix *}
				{*{input $name, type=>$type, class=>'inp-text', data-inp-phone-target=>'inp', placeholder=>$placeholder, data-action=>$dataAction, aria-describedby=>$name.'_error'}*}
				{input $name, type=>$type, class=>'inp-text', placeholder=>$placeholder, data-action=>$dataAction, aria-describedby=>$name.'_error'}
				<span id="{$name.'_error'}" n:if="$validate && $showError" class="inp-error u-d-n">{_'error_invalid_phone'}</span>
				<label n:name="{$name}" n:class="inp-label, $labelClass">{$label|noescape}</label>
			</span>
		{else}
			<span class="inp-fix">
				{input $name class=>implode(' ', ['inp-text', $inpClass]), cols=>$cols, rows=>$rows, disabled=>$disabled, placeholder=>$placeholder, data-action=>$dataAction, aria-describedby=>$name.'_error'}
				<span id="{$name.'_error'}" n:if="$validate && $showError" class="inp-error"></span>
				<label n:name="{$name}" n:class="inp-label, $labelClass">{$label|noescape}</label>
			</span>
		{/if}
	{/if}
</p>
