{php $control['form']->action .= "#frm-communityForm-form"}

{snippet form}
	{form form class: 'f-community u-mb-lg block-loader', data-naja: '', novalidate: "novalidate"}
		{* <h2>
			{_newsletter_title}
		</h2> *}

		{control messageForForm, $flashes, $form, TRUE, ['timeToggle'=>true]}

		{include '../inp.latte', form: $form, name: email, labelLang: 'form_label_email', placeholderLang=>'form_label_email', validate: true}

		<p>
			<button type="submit" class="btn">
				<span class="btn__text">
					{_btn_login}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

		{if isset($flashes[0]) && $flashes[0]->type == "ok" && $flashes[0]->message == "communityAdded"}
			<script>
				dataLayer.push({
					'event' : 'action.optin.community'
				});
			</script>
		{/if}
	{/form}
{/snippet}
