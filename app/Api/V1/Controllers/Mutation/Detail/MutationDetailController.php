<?php

declare(strict_types=1);

namespace App\Api\V1\Controllers\Mutation\Detail;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Controllers\Mutation\MutationItem;
use App\Model\Orm\Orm;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Mutation')]
final class MutationDetailController extends BaseV1Controller
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	#[Path('/mutation/{id}')]
	#[Method('GET')]
	#[RequestParameter(name: 'id', type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Success', code: '200', entity: MutationItem::class)]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$id = $request->getParameter('id');
		$mutation = $this->orm->mutation->getById($id);

		return $this->jsonResponse(
			(new MutationItem($mutation))->toResponse(),
			$response,
		);
	}
}
