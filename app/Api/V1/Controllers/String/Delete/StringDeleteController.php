<?php

declare(strict_types=1);

namespace App\Api\V1\Controllers\String\Delete;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestBody;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Controllers\String\StringInputData;
use App\Api\V1\Controllers\String\StringItem;
use App\Model\Orm\Orm;
use App\Model\Orm\String\StringEntity;

#[Path('/')]
#[Tag('String')]
final class StringDeleteController extends BaseV1Controller
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	#[Path('/string/{name}')]
	#[Method('DELETE')]
	#[RequestParameter(name: 'name', type: 'string', in: EndpointParameter::IN_PATH, required: true)]
	#[Response(description: 'Success', code: '200')]
	public function delete(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$name = $request->getParameter('name');
		$this->orm->string->delete($name);

		$this->orm->flush();

		return $response->withStatus($response::S204_NO_CONTENT);
	}
}
