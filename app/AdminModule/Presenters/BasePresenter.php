<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters;

use App\Infrastructure\Latte\Filters;
use App\Model\Mutation\MutationHolder;
use App\Model\Security\Acl;
use App\Model\Translator;
use App\Model\TranslatorDB;
use	Nette;
use Nette\Application\UI\Template;
use Nette\DI\Attributes\Inject;

abstract class BasePresenter extends \App\BasePresenter
{
	#[Inject]
	public Translator $translator;

	#[Inject]
	public TranslatorDB $translatorDb;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public AdminAccessChecker $accessChecker;

	protected function startup(): void
	{
		$defaultMutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($defaultMutation);
		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setPublicOnly(false);

		$ipAddress = $this->getHttpRequest()->getRemoteAddress();
		if ($ipAddress !== null && ! $this->accessChecker->isAllowed($ipAddress)) {
			$this->error(httpCode: Nette\Http\Response::S404_NotFound);
		}

		parent::startup();

		// uploadify - dira jako bejk
		if (!isset($_REQUEST['upSessionID'])) {
			$this->isLogin();
		}
	}

	protected function createTemplate(): Nette\Application\UI\Template
	{
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		return $template;
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();

		// ******* basic ************************
		$this->template->isDeveloper = $this->user->isDeveloper();
		$this->template->config = $this->configService->getParams();
		$this->template->staticFile = 'config/' . $this->configService->getParam('adminAlias');
		$this->template->title = $this->configService->getParam('adminTitle');
		$this->template->pageTabs = $this->configService->get('tabs');
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->translator = $this->translator;
		if (isset($this->object)) {
			$this->template->object = $this->object;
		} else {
			$this->template->object = null;
		}
		$this->template->menu = $this->getMenu();
		$this->template->envName = $this->configService->get('envName');
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);
		$this->template->mutations = $this->orm->mutation->findAll();

		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;

		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');

	}

	public function flashMessage($message, string $type = "info"): \stdClass
	{
		$message = $this->translator->translate($message);
		return parent::flashMessage($message, $type);
	}

	/**
	 * ověří, zda-li uživatel přihlášen a má právo pro AdminModule
	 * pokud ne, přesměruje na přihlášení
	 *
	 * @throws Nette\Application\AbortException
	 * @throws Nette\Application\ForbiddenRequestException
	 */
	protected function isLogin(): void
	{
		if ($this->presenter->name === 'Admin:Sign') {
			return;
		}

		$this->userEntity = $this->orm->user->getById($this->user->getId());
		if ($this->userEntity === null) {
			$this->flashMessage('msg_signet_out_inactive', 'info');
			$this->user->logout(true);
			$this->redirect(':Admin:Sign:default', ['backlink' => $this->storeRequest()]);
		}

		if (!$this->user->isLoggedIn() || !$this->user->isAllowed(Acl::RES_ADMIN)) {
			if ($this->user->getLogoutReason() === Nette\Security\UserStorage::LOGOUT_INACTIVITY) {
				$this->flashMessage('msg_signet_out_inactive', 'info');
			} elseif ($this->user->isLoggedIn() && !$this->user->isAllowed(Acl::RES_ADMIN)) {
				$this->flashMessage('msg_access_denied', 'error');
			}

			$this->redirect(':Admin:Sign:default', ['backlink' => $this->storeRequest()]);
		}

		//možná předěláme a bude se volat přímo
		$this->checkPermission($this->presenter->name);
	}


	protected function checkPermission(mixed $resource, mixed $privilege = Nette\Security\Authorizator::ALL): void
	{
		if (!$this->user->isAllowed($resource, $privilege)) {
			throw new Nette\Application\ForbiddenRequestException();
		}
	}

	/**
	 * vrací hlavní menu
	 */
	protected function getMenu(): array
	{
		$menu = [];

		foreach ($this->configService->get('adminMenu') as $sectionName => $items) {
			foreach ($items as $item) {
				if ($this->user->isAllowed($item['resource'])) {
					$menu[$sectionName][] = $item;
				}
			}
		}

		return $menu;
	}

	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}

	public function formatLayoutTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$layout = $this->layout ?: 'layout';

		return [
			"$dir/templates/@$layout.latte",
			__DIR__ . "/../templates/@$layout.latte",
		];
	}
}
