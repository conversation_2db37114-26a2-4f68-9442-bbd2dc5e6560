<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Catalog\Components\DataGrid;

use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource\DataMapper;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Translator;
use Elasticsearch\ClientBuilder;
use Nette\Application\UI\Control;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Ublaboo\DataGrid\Filter\FilterSelect;
use Ublaboo\DataGrid\Utils\Sorting;

class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$csMutation = $this->mutationsHolder->getDefault();
		$esIndex = $this->orm->esIndex->getAllLastActive($csMutation);

		if ($esIndex === null) {
			$grid = new \Ublaboo\DataGrid\DataGrid();
			$grid->setDataSource([]);
			$grid->addColumnText('id', 'id');
			return $grid;
		}

		$client = ClientBuilder::create()->build();




		$dataSource = new DataSource\ElasticsearchDataSource(
			$client, // Elasticsearch\Client
			$esIndex->esName, // Index name
			$this->getDataMappers(),
		);

		$grid = new \Ublaboo\DataGrid\DataGrid();



		$grid->setStrictSessionFilterValues(false);
		$baseFilter = new FilterSelect($grid, 'type', 'type', [
			'type' => 'product',
		], 'type');

		$baseFilter = $baseFilter->setValue('product');
		$dataSource->applyFilterSelect($baseFilter);
		$baseSort = new Sorting(['nameSort' => 'asc']);
		$dataSource->sort($baseSort);

		$grid->setDataSource($dataSource);
		$grid->setItemsPerPageList([30, 50], false);

		$grid->addColumnText('id', 'id')->setSortable()->setFilterText()->setExactSearch();
		$grid->addColumnText('nameSort', 'nameSort')->setSortable()->setFilterText();

		$parameter = $this->orm->parameter->getBy(['uid' => 'manufacturer']);
		if ($parameter !== null) {
			$manufacturerUid = 'manufacturer';
			$grid->addColumnText($manufacturerUid, 'ublaboo_' . $manufacturerUid, 'filter.' . $manufacturerUid)
				->setRenderer(function ($data) use ($manufacturerUid) {
					return $data['filter'][$manufacturerUid] ?? '';
				})
				->setFilterMultiSelect($this->getOptions($parameter))
				->setPrompt('Vše');
		}

		$grid->addColumnText('publish', 'ublaboo_publish', 'filter.publish')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publish']) && $data['filter']['publish']) {
					foreach ($data['filter']['publish'] as $publish) {
						if (preg_match('/(.*)_public$/', $publish, $matches)) {
							$parts[] = $matches[1];
						}
					}
				}

				return implode(', ', $parts);
			})
			->setFilterMultiSelect($this->getPublishOptions())->setPrompt('Vše');

		$grid->addColumnText('isInStock', 'ublaboo_isInStock', 'filter.isInStock')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isInStock']) && $data['filter']['isInStock']) ? 'Skladem' : 'Vyprodáno';
			})
			->setFilterSelect([
				1 => 'Skladem',
				0 => 'Vyprodáno',
			])->setPrompt('Vše');

		$grid->addColumnText('isNew', 'ublaboo_isNew', 'filter.isNew')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isNew']) && $data['filter']['isNew']) ? 'Novinka' : '';
			})
			->setFilterSelect([
				1 => 'Novinky',
				0 => 'Staré',
			])->setPrompt('Vše');

		$grid->addColumnText('isOld', 'ublaboo_isOld', 'filter.isOld')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isOld']) && $data['filter']['isOld']) ? 'Prodej ukončen' : '';
			})
			->setFilterSelect([
				1 => 'Prodej ukončen',
				0 => 'Prodávané',
			])->setPrompt('Vše');

		$grid->addColumnText('isAction', 'ublaboo_isAction', 'filter.isAction')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isAction']) && $data['filter']['isAction']) ? 'Akce' : '';
			})
			->setFilterSelect([
				1 => 'V akci',
				0 => 'Bez akce',
			])->setPrompt('Vše');

		$grid->addColumnDateTime('public', 'public', 'filter.publishDate')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publishDate'][0])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][0])->format('d.m.Y');
				}

				if (isset($data['filter']['publishDate'][1])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][1])->format('d.m.Y');
				}

				return implode(' - ', $parts);
			})

			->setFilterDateRange('filter.publishDate');

		$grid->addAction('edit', 'Edit', 'Product:edit')->setClass('btn btn-xs btn-primary');

		$grid->setDefaultSort(['nameSort' => 'asc']);

		$grid->setTranslator($this->translator);

		return $grid;
	}

	private function getOptions(Parameter $parameter): array
	{
		$options = [];
		foreach ($parameter->options as $option) {
			$options[(string) $option->internalValue] = (string) $option->internalValue;
		}

		return $options;
	}


	private function getPublishOptions(): array
	{
		$rows = [];
		foreach ($this->mutationsHolder->findAll(false) as $mutation) {
			$rows[sprintf('%s_public', $mutation->langCode)] = sprintf('%s - publikováno', $mutation->langCode);
			$rows[sprintf('%s_hide', $mutation->langCode)] = sprintf('%s - skryto', $mutation->langCode);

		}

		return $rows;
	}

	private function getDataMappers(): array
	{
		$mappers = [];


		// add boost
		$template['term']['type'] = ['value' => null, 'boost' => 10];
		$mappers[] = new DataMapper(['[term][type][query]' => '[term][type][value]'], $template );

		// {"range":{"filter.publishDate":{"gte":1625349600,"lte":1654379999}}}
		// split and switch
		$mappers[] = new DataMapper([
			'[range][filter.publishDate][gte]' => '[0][range][filter.publishDate][gte]',
			'[range][filter.publishDate][lte]' => '[1][range][filter.publishDate][lte]'
			] );


		// fix old 'should' syntax for ES
		$mappers[] = new DataMapper([
			'[bool][should][0]' => '[bool][should]',
		] );

		return $mappers;
	}

}
