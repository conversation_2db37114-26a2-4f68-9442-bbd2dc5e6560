<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\FileLibrary\Components\DataGrid;

use App\Model\Orm\File\File;
use App\Model\Orm\File\FileModel;
use App\Model\Orm\File\FileRepository;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use LogicException;
use Nette\Application\UI\Control;
use Nette\Utils\Html;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{


	public function __construct(
		private readonly bool $isDeveloper,
		private readonly Translator $translator,
		private readonly FileRepository $fileRepository,
		private readonly FileModel $fileModel,
		private readonly Orm $orm,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		if ($this->isDeveloper) {
			$options = [];
		} else {
			$options = ['isDeleted' => 0];
		}
		$files = $this->fileRepository->findBy($options)->orderBy(
			['id' => 'DESC']
		);
		$grid->setDataSource($files);

		$grid->addColumnText('id', 'id')->setSortable()->setFilterText();
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('url', 'URL')
			->setRenderer(function ($row) {
				if ($row->isDeleted) {
					return $row->url;
				} else {
					$name = basename($row->url);
					return Html::el('a')
						->setHref($row->url)
						->setText($row->url)
						->setAttribute('download', $name)
						->setTarget('_blank');
				}
			})->setSortable()->setFilterText('filename');

		if ($this->isDeveloper) {
			$grid->addColumnText('isDeleted', 'smazáno')->setSortable()->setFilterText();
		}

//		$grid->addColumnText('usage', 'Použito')
//			->setRenderer(function ($row) {
//				$mutation = $this->orm->mutation->getRsDefault();
//				if ($this->fileModel->getFileUsage($row, $mutation)) {
//					return "";
//				} else {
//					return "NE";
//				}
//			});

		$grid->addAction('delete', 'delete_button', 'delete!')
			->setClass('btn btn-xs btn-danger')
			->setRenderCondition(function(File $row) {
				if ($row->isDeleted) {
					return false;
				}
				$mutation = $this->orm->mutation->getRsDefault();
				return $this->fileModel->getFileUsage($row, $mutation) ? false : true;
			})
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return $this->translator->translate('delete_confirm') . ' [' . $item->name . ']';
					}
				)
			);

		$grid->setTranslator($this->translator);

		return $grid;
	}


	public function handleDelete(string $id): void
	{
		$file = $this->orm->file->getById($id);
		$this->fileModel->markAsDelete($file);
		$this->presenter->redirect('this');
	}
}
