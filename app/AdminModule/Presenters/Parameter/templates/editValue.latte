{block #content}
	<div class=""  data-controller="Templates ModalClose" data-action="ImageLibrary:newItem@window->Templates#newItem">

		{varType App\Model\Orm\Parameter\Parameter $parameter}
		{varType App\Model\Orm\ParameterValue\ParameterValue $parameterValue}
		<h1><a n:href="default">Parametry</a> - <a n:href="edit, id=> $parameter->id">{$parameter->name}</a> - {$parameterValue->value}</h1>


		{control parameterValueForm}

		{include $templates . '/part/core/libraryOverlay.latte'}
	</div>
