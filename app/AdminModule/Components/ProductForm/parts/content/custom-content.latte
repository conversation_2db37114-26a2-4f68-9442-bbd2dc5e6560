{var $icon = $templates.'/part/icons/grip-vertical.svg'}


{var $props = [
	title: '<PERSON><PERSON><PERSON><PERSON><PERSON>lý modulární obsah',
	id: 'customContent',
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	rowMain: false,
]}



{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0">
			{var $gridSize = 12}
			{if $form['productLocalizations']->components->count() == 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $localization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation])}
				{var $langCode = $mutation->langCode}
				<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
					<div class="row-main">
						<div class="b-custom-field-header u-mb-xs">
							<div class="tag b-custom-field-header__lang">
								{$langCode}
							</div>

							<div class="b-custom-field-header__copy" data-controller="CustomFieldsCopy" data-customfieldscopy-type-value="cc" data-customfieldscopy-to-lang-value="{$langCode}">
								<span>Zkopírovat obsah z jazyka:</span>
								<div class="inp-fix">
									<select data-customfieldscopy-target="select" class="inp-select">
										<option value="{$otherMutation->langCode}" n:foreach="$mutations->findBy(['id!=' => $mutation->id]) as $otherMutation">{$otherMutation->langCode}</option>
									</select>
									<div class="inp-text__holder"></div>
								</div>
								<button type="button" class="btn btn--sm" data-action="CustomFieldsCopy#copy">
									<span class="btn__text">Zkopírovat</span>
								</button>
							</div>
						</div>

						{include $templates.'/../../PostType/Core/AdminModule/Components/Form/parts/content/custom-content-element.latte',
							class=>'',
							langCode=>$mutation->langCode,
							mutationId=>$mutation->id,
							scheme=>$localization->getCcSchemeJson(),
							values=>$localization->getCcJson(),
							modules=>$localization->getCcModulesJson(),
							uploadLink=>$fileUploadLink,
							itemName=>'productLocalizations-'.$mutationId.'-cc',
							customFieldsType=>'cc'
						}
					</div>
				</div>
			{/foreach}
		</div>
	{/block}
{/embed}
