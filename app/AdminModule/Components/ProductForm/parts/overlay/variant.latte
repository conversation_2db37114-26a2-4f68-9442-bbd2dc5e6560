{formContainer $variantContainer}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Publikace v modifikacích',
		id: 'variant-public-'.$variantId,
		open: true,
		tags: [
			[text: 'Lokalizované']
		]
	], templates=>$templates}
		{block content}
			{foreach $mutations as $mutation}
				{var $langCode = $mutation->langCode}
				<div class="js-lang js-lang--{$langCode}">
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $variantContainer['variantLocalizations'][$mutation->id]['active'],
							label: '<span class="grid-inline"><span class="tag">'.$langCode.'</span> <span>Aktivováno</span></span>',
							dataInp: [
								action: 'change->ProductVariantEdit#edit',
								ProductVariantEdit-target: 'lang',
								lang: $langCode,
							]
						]
					}
				</div>
			{/foreach}
		{/block}
	{/embed}


	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Cenové hladiny (ceny bez DPH)',
		open: true,
		id: 'variant-price-'.$variantId,
		tags: [
			[text: 'Lokalizované']
		]
	], templates=>$templates}
		{block content}
			{foreach $mutations as $mutation}
				<div class="u-mb-sm js-lang js-lang--{$mutation->langCode|lower}">
					<div class="tag u-mb-xxs">
						{$mutation->langCode}
					</div>
					<div class="grid">
						{foreach $priceLevels as $priceLevel}
							<div class="grid__cell size--6-12">
								{include $templates.'/part/core/inp.latte',
									props: [
										classes: [],
										label: $priceLevel->name,
										classesLabel: ['title'],
										input: $variantContainer['variantPrices'][$mutation->id][$priceLevel->id]['price'],
										type: 'number',
										prefix: $mutation->currency->getCurrencyCode(),
										dataInp: [
										ProductVariantEdit-target: 'price',
										action: 'input->ProductVariantEdit#edit',
										lang: $mutation->langCode,
										currency: $mutation->currency->getCurrencyCode(),
									],
								]}
							</div>
						{/foreach}
					</div>
				</div>
			{/foreach}
		{/block}
	{/embed}

	{embed $templates.'/part/box/toggle.latte', props=>[
	title: 'Sklady',
	open: true,
	id: 'variant-supply-'.$variantId,
	], templates=>$templates}
		{block content}
			{foreach $stocks as $stock}
				<div class="u-mb-sm">
					{var $variant = $product->variants->toCollection()->getById($variantId)}
					{if $variant}
						{capture $variantInfo}
							{_'product_stock_last_import'}:
							{if isset($variant->suppliesByStock[$stock->id]) && $variant->suppliesByStock[$stock->id]->lastImport}{$variant->suppliesByStock[$stock->id]->lastImport|date:'j.n.Y H:i:s'}{else}--{/if}
						{/capture}
					{/if}
					{include $templates.'/part/core/inp.latte', props: [
						classes: ['u-mb-xs'],
						input: $variantContainer['variantSupplies'][$stock->id]['amount'],
						label: $stock->name,
						classesLabel: ['title'],
						prefix: 'ks',
						dataInp: [
							ProductVariantEdit-target: 'supply',
							action: 'input->ProductVariantEdit#edit',
						],
						info: $variant ? $variantInfo : ''
					]}
				</div>
			{/foreach}
		{/block}
	{/embed}

	{if $variantCommonContainer = $variantContainer->getComponent('variantCommon')}
		{formContainer $variantCommonContainer}
			{foreach $variantCommonContainer->getComponents() as $item}
				{if $item->options['type'] == 'text'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						classesLabel: ['title']
					]}
				{elseif $item->options['type'] == 'checkbox'}
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $item
						]
					}
				{elseif $item->options['type'] == 'select'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						type: 'select',
						classesLabel: ['title']
					]}
				{else}
					<div>
						{label $item /}
						{input $item}
					</div>
				{/if}
			{/foreach}
		{/formContainer}
	{/if}
{/formContainer}
