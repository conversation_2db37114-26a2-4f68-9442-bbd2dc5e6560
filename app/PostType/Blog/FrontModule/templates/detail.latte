{varType App\PostType\Blog\Model\Orm\BlogLocalization $object}

{block content}
	{include $templates.'/part/box/annot.latte', class: 'b-annot--article', titleClass: 'h2'}
	{embed $templates.'/part/box/article-content.latte', class: 'u-mb-4xl'}
		{block content}
			<div class="u-mb-last-0 u-mb-3xl">
				{control customContentRenderer}
			</div>
			{include $templates.'/part/box/share.latte'}
			{include $templates.'/part/crossroad/authors.latte'}
		{/block}
	{/embed}
{/block}

{* {include $templates.'/part/crossroad/tags.latte', items: $object->blogTags} *}
{* <h2>
	{_"title_categories"}
</h2>
<ul n:ifcontent class="u-mb-lg">
	<li n:foreach="$object->categories as $category">
		<a n:href="$category">{$category->name}</a>
	</li>
</ul> *}
{* {control attachedBlogs} *}