<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\AdminModule\Components\Form;

use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Handler as CoreHandler;
use App\PostType\SeoLink\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\SeoLink\Model\Orm\SeoLink;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly Orm $orm,
		private readonly CoreHandler $coreHandler,
	)
	{
	}

	public function handle(SeoLinkLocalization $seoLinkLocalization, User $user, BaseFormData $data, ArrayHash $postData): void
	{
		$this->coreHandler->handleParent($seoLinkLocalization->seoLink, $data->parent);
		$this->coreHandler->handleLocalization($seoLinkLocalization, $data->localization);
		$this->coreHandler->handleRoutable($seoLinkLocalization, $data->localization, $data->routable);

		$this->handleParameterValues($seoLinkLocalization->seoLink, $postData);

		$this->orm->seoLinkLocalization->persistAndFlush($seoLinkLocalization);
	}

	private function handleParameterValues(SeoLink $seoLink, ArrayHash $values): void
	{
		$parameterValues = [];
		if (isset($values->parameterValues)) {
			foreach ($values->parameterValues as $parameterValueRow) {
				$parameterValue = $this->orm->parameterValue->getById($parameterValueRow->id);
				if ($parameterValue !== null) {
					$parameterValues[$parameterValueRow->id] = $parameterValue;
				}
			}
		}

		$seoLink->parameterValues->set($parameterValues);
	}

}
