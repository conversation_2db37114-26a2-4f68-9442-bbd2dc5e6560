<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection|SeoLink[] searchByName(int $id, int $mutationId, array $excluded)
 * @method ICollection|SeoLink[] findByFilter(object $filter)
 */
final class SeoLinkRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [SeoLink::class];
	}

}
