<?php declare(strict_types=1);

namespace App\PostType\Workshop\FrontModule\Components\WorkshopLectors;

use App\Model\TranslatorDB;
use App\PostType\Workshop\Model\Orm\WorkshopLocalization;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class WorkshopLectors extends Control
{

	public function __construct(
		private readonly WorkshopLocalization $workshop,
		private readonly TranslatorDB $translator
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->workshop = $this->workshop;
		$this->template->lectors = $this->workshop->lectors;
		$this->template->render(__DIR__ . '/workshopLectors.latte');
	}
}
