cf:
	templates:
		workshop:
			info:
				type: group
				items:
					mainImage:
						type: image
						label: "<PERSON><PERSON><PERSON><PERSON> (min. 2880x1620)"

		workshopLocalization:
			info:
				type: group
				items:
					place:
						type: text
						label: "<PERSON>ís<PERSON>"
					placeInfo:
						type: textarea
						label: "Informace k místu (v pravím sticky sloupci)"
					info:
						type: textarea
						label: "Dodatečné info (v pravím sticky sloupci)"
					length:
						type: text
						label: "Trvání"
					content:
						type: tinymce
						label: "Popis workshopu"
			workshopDates:
				type: list
				label: "Termíny"
				items:
					from:
						type: dateTime
						label: "Od"
					to:
						type: dateTime
						label: "Do"

			# content: @cf.definitions.content
			lectors:
				type: list
				label: "Lektoři"
				items:
					name:
						type: text
						label: "J<PERSON><PERSON>"
					position:
						type: text
						label: "Pozice"
					image:
						type: image
						label: "Fotka (min. 560x720)"

			program:
				type: list
				label: "Program"
				items:
					title:
						type: text
						label: "Název pokrmu"
					content: @cf.definitions.content
					image:
						type: image
						label: "<PERSON><PERSON><PERSON> (min. 320x284)"
					category:
						type: text
						label: "Kategorie"
			benefits:
				type: list
				label: "Výhody"
				items:
					title:
						type: text
						label: "Nadpis"
					content: @cf.definitions.content
					image:
						type: image
						label: "Fotka (min. 1200x800)"

			aboutPlace:
				type: group
				label: "O místě konání"
				items:
					image:
						type: image
						label: "Obrázek pod nadpisem (min. 320x480)"
					content:
						type: tinymce
						label: "Obsah"
					benefits:
						type: list
						label: "Benefity (popis)"
						items:
							title:
								type: text
								label: "Nadpis"
							annot:
								type: textarea
								label: "Anotace"

			aboutWorkshop:
				type: group
				label: "O workshopu"
				items:
					image:
						type: image
						label: "Obrázek pod nadpisem (min. 320x480)"
					content:
						type: tinymce
						label: "Obsah"

cc:
	templates:

application:
	mapping:
		Workshop: App\PostType\Workshop\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Workshop: workshop

services:
	## Components
	- App\PostType\Workshop\FrontModule\Components\WorkshopDates\WorkshopDatesFactory
	- App\PostType\Workshop\FrontModule\Components\WorkshopLectors\WorkshopLectorsFactory
	- App\PostType\Workshop\FrontModule\Components\WorkshopProgram\WorkshopProgramFactory
	- App\PostType\Workshop\FrontModule\Components\WorkshopTopics\WorkshopTopicsFactory

	- App\PostType\Workshop\AdminModule\Components\DataGrid\WorkshopApplicant\ApplicantDataGridFactory

	## Facade
	- App\PostType\Workshop\Model\WorkshopLocalizationFacade
