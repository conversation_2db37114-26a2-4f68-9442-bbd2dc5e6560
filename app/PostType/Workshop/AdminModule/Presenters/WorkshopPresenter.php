<?php declare(strict_types=1);

namespace App\PostType\Workshop\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Workshop\Model\Orm\WorkshopLocalization;
use App\PostType\Workshop\Model\WorkshopLocalizationFacade;

final class WorkshopPresenter extends BasePresenter
{
	private const string OrmRepositoryName = 'workshop';

	private WorkshopLocalization $workshopLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly WorkshopLocalizationFacade $workshopLocalizationFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}

	public function actionEdit(int $id): void
	{
		if (($this->workshopLocalization = $this->orm->workshopLocalization->getById($id)) === null) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::OrmRepositoryName, $this->orm->workshopLocalization->findAll());
	}

	protected function createComponentApplicantsGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::OrmRepositoryName, $this->orm->workshopApplicant->findAll());
	}

	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->workshopLocalizationFacade, $this->workshopLocalization, $userEntity);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, entityLocalizationFacade: $this->workshopLocalizationFacade);
	}


}
