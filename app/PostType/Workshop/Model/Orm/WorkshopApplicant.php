<?php declare(strict_types=1);

namespace App\PostType\Workshop\Model\Orm;

use App\Model\Orm\BackedEnumWrapper;
use App\Model\Orm\BaseEntity;
use App\PostType\Workshop\Model\Enum\ApplicantExperience;

/**
 * @property string $name
 * @property string $bakery
 * @property string $email
 * @property string $phone
 * @property string $message
 *
 * @property ApplicantExperience $experience {wrapper BackedEnumWrapper}
 *
 * RELATIONS
 * @property Workshop $workshop {m:1 Workshop::$applicants}
 */
class WorkshopApplicant	extends BaseEntity
{
}
