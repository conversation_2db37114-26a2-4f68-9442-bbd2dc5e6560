<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BlogTagLocalization getById($id)
 * @method BlogTagLocalization[]|ICollection searchByName(string $q, array $excluded)
 * @method array findAllIds(?int $limit)
 */
final class BlogTagLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [BlogTagLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof BlogTagLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
