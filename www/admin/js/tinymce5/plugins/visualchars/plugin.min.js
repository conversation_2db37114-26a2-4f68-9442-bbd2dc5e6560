/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(r){"use strict";var n,e,t,o,u,i,c=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return c(t())}}},a=tinymce.util.Tools.resolve("tinymce.PluginManager"),f=function(n){return{isEnabled:function(){return n.get()}}},l=function(n,e){return n.fire("VisualChars",{state:e})},s={"\xa0":"nbsp","\xad":"shy"},d=function(n,e){var t,r="";for(t in n)r+=t;return new RegExp("["+r+"]",e?"g":"")},m=function(n){var e,t="";for(e in n)t&&(t+=","),t+="span.mce-"+n[e];return t},N={charMap:s,regExp:d(s),regExpGlobal:d(s,!0),selector:m(s),charMapToRegExp:d,charMapToSelector:m},g=function(n){return function(){return n}},E=g(!1),h=g(!0),p=E,v=h,T=function(){return O},O=(o={fold:function(n,e){return n()},is:p,isSome:p,isNone:v,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:t,orThunk:e,map:T,ap:T,each:function(){},bind:T,flatten:T,exists:p,forall:v,filter:T,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:g("none()")},Object.freeze&&Object.freeze(o),o),y=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:v,isNone:p,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return y(n(t))},ap:function(n){return n.fold(T,function(n){return y(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:O},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(p,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},D=function(n){return null===n||n===undefined?O:y(n)},_=(u="function",function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===u}),C=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},M=(Array.prototype.slice,_(Array.from)&&Array.from,function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:g(n)}}),A={fromHtml:function(n,e){var t=(e||r.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw r.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return M(t.childNodes[0])},fromTag:function(n,e){var t=(e||r.document).createElement(n);return M(t)},fromText:function(n,e){var t=(e||r.document).createTextNode(n);return M(t)},fromDom:M,fromPoint:function(n,e,t){var r=n.dom();return D(r.elementFromPoint(e,t)).map(M)}},b=(r.Node.ATTRIBUTE_NODE,r.Node.CDATA_SECTION_NODE,r.Node.COMMENT_NODE,r.Node.DOCUMENT_NODE,r.Node.DOCUMENT_TYPE_NODE,r.Node.DOCUMENT_FRAGMENT_NODE,r.Node.ELEMENT_NODE,r.Node.TEXT_NODE),S=(r.Node.PROCESSING_INSTRUCTION_NODE,r.Node.ENTITY_REFERENCE_NODE,r.Node.ENTITY_NODE,r.Node.NOTATION_NODE,function(n){return n.dom().nodeValue}),k=(i=b,function(n){return n.dom().nodeType===i}),w=function(n){return'<span data-mce-bogus="1" class="mce-'+N.charMap[n]+'">'+n+"</span>"},x=function(n,e){var t=[],r=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o,n)}return r}(n.dom().childNodes,A.fromDom);return C(r,function(n){e(n)&&(t=t.concat([n])),t=t.concat(x(n,e))}),t},P={isMatch:function(n){return k(n)&&S(n)!==undefined&&N.regExp.test(S(n))},filterDescendants:x,findParentElm:function(n,e){for(;n.parentNode;){if(n.parentNode===e)return n;n=n.parentNode}},replaceWithSpans:function(n){return n.replace(N.regExpGlobal,w)}},I=function(t,n){var r,o,e=P.filterDescendants(A.fromDom(n),P.isMatch);C(e,function(n){var e=P.replaceWithSpans(S(n));for(o=t.dom.create("div",null,e);r=o.lastChild;)t.dom.insertAfter(r,n.dom());t.dom.remove(n.dom())})},B=function(e,n){var t=e.dom.select(N.selector,n);C(t,function(n){e.dom.remove(n,1)})},R=I,U=B,V=function(n){var e=n.getBody(),t=n.selection.getBookmark(),r=P.findParentElm(n.selection.getNode(),e);r=r!==undefined?r:e,B(n,r),I(n,r),n.selection.moveToBookmark(t)},j=function(n,e){var t,r=n.getBody(),o=n.selection;e.set(!e.get()),l(n,e.get()),t=o.getBookmark(),!0===e.get()?R(n,r):U(n,r),o.moveToBookmark(t)},q=function(n,e){n.addCommand("mceVisualChars",function(){j(n,e)})},G=tinymce.util.Tools.resolve("tinymce.util.Delay"),H=function(e,t){var r=G.debounce(function(){V(e)},300);!1!==e.settings.forced_root_block&&e.on("keydown",function(n){!0===t.get()&&(13===n.keyCode?V(e):r())})},L=function(n){return n.getParam("visualchars_default_state",!1)},F=function(e,t){e.on("init",function(){var n=!L(e);t.set(n),j(e,t)})},Y=function(t,r){return function(e){e.setActive(r.get());var n=function(n){return e.setActive(n.state)};return t.on("VisualChars",n),function(){return t.off("VisualChars",n)}}};a.add("visualchars",function(n){var e,t,r=c(!1);return q(n,r),t=r,(e=n).ui.registry.addToggleButton("visualchars",{tooltip:"Show invisible characters",icon:"paragraph",onAction:function(){return e.execCommand("mceVisualChars")},onSetup:Y(e,t)}),e.ui.registry.addToggleMenuItem("visualchars",{text:"Show invisible characters",onAction:function(){return e.execCommand("mceVisualChars")},onSetup:Y(e,t)}),H(n,r),F(n,r),f(r)}),function z(){}}(window);