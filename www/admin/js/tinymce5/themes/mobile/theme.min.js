/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.1 (2019-02-21)
 */
!function(n,v){"use strict";var y=function(){return(y=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function c(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&(t[r[o]]=n[r[o]])}return t}var x=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]},p=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},h=function(n){return function(){return n}},b=function(n){return n};function l(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var e,t,r,o,i,u,a,S=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}},f=function(n){return function(){throw new Error(n)}},s=function(n){return n()},d=h(!1),m=h(!0),g=d,w=m,O=function(){return T},T=(o={fold:function(n,e){return n()},is:g,isSome:g,isNone:w,getOr:r=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:r,orThunk:t,map:O,ap:O,each:function(){},bind:O,flatten:O,exists:g,forall:w,filter:O,equals:e=function(n){return n.isNone()},equals_:e,toArray:function(){return[]},toString:h("none()")},Object.freeze&&Object.freeze(o),o),k=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:w,isNone:g,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return k(n(t))},ap:function(n){return n.fold(O,function(n){return k(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:T},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(g,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},E={some:k,none:O,from:function(n){return null===n||n===undefined?T:k(n)}},C=Object.keys,D=Object.hasOwnProperty,M=function(n,e){for(var t=C(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i,n)}},A=function(n,r){return I(n,function(n,e,t){return{k:e,v:r(n,e,t)}})},I=function(r,o){var i={};return M(r,function(n,e){var t=o(n,e,r);i[t.k]=t.v}),i},B=function(n,t){var r=[];return M(n,function(n,e){r.push(t(n,e))}),r},R=function(n,e){return D.call(n,e)},F=h("touchstart"),V=h("touchmove"),N=h("touchend"),H=h("mousedown"),j=h("mousemove"),z=h("mouseup"),L=h("mouseover"),P=h("keydown"),U=h("keyup"),G=h("input"),$=h("change"),W=h("click"),_=h("transitionend"),q=h("selectstart"),X=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},Y=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return J(r(1),r(2))},K=function(){return J(0,0)},J=function(n,e){return{major:n,minor:e}},Q={nu:J,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?K():Y(n,t)},unknown:K},Z="Edge",nn="Chrome",en="Opera",tn="Firefox",rn=function(n,e){return function(){return e===n}},on=function(n){var e=n.current;return{current:e,version:n.version,isEdge:rn(Z,e),isChrome:rn(nn,e),isIE:rn("IE",e),isOpera:rn(en,e),isFirefox:rn(tn,e),isSafari:rn("Safari",e)}},un={unknown:function(){return on({current:undefined,version:Q.unknown()})},nu:on,edge:h(Z),chrome:h(nn),ie:h("IE"),opera:h(en),firefox:h(tn),safari:h("Safari")},an="Windows",cn="Android",fn="Solaris",sn="FreeBSD",ln=function(n,e){return function(){return e===n}},dn=function(n){var e=n.current;return{current:e,version:n.version,isWindows:ln(an,e),isiOS:ln("iOS",e),isAndroid:ln(cn,e),isOSX:ln("OSX",e),isLinux:ln("Linux",e),isSolaris:ln(fn,e),isFreeBSD:ln(sn,e)}},mn={unknown:function(){return dn({current:undefined,version:Q.unknown()})},nu:dn,windows:h(an),ios:h("iOS"),android:h(cn),linux:h("Linux"),osx:h("OSX"),solaris:h(fn),freebsd:h(sn)},gn=function(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===e}},pn=gn("string"),vn=gn("object"),hn=gn("array"),bn=gn("boolean"),yn=gn("function"),xn=gn("number"),wn=(i=Array.prototype.indexOf)===undefined?function(n,e){return In(n,e)}:function(n,e){return i.call(n,e)},Sn=function(n,e){return-1<wn(n,e)},On=function(n,e){return An(n,e).isSome()},Tn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o,n)}return r},kn=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},En=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r,n)&&t.push(i)}return t},Cn=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--)e(n[t],t,n)}(n,function(n){t=e(t,n)}),t},Dn=function(n,e,t){return kn(n,function(n){t=e(t,n)}),t},Mn=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t,n))return E.some(o)}return E.none()},An=function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t,n))return E.some(t);return E.none()},In=function(n,e){for(var t=0,r=n.length;t<r;++t)if(n[t]===e)return t;return-1},Bn=Array.prototype.push,Rn=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!Array.prototype.isPrototypeOf(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);Bn.apply(e,n[t])}return e},Fn=function(n,e){var t=Tn(n,e);return Rn(t)},Vn=function(n,e){for(var t=0,r=n.length;t<r;++t)if(!0!==e(n[t],t,n))return!1;return!0},Nn=Array.prototype.slice,Hn=function(n){var e=Nn.call(n,0);return e.reverse(),e},jn=function(n){return[n]},zn=(yn(Array.from)&&Array.from,function(n,e){var t=String(e).toLowerCase();return Mn(n,function(n){return n.search(t)})}),Ln=function(n,t){return zn(n,t).map(function(n){var e=Q.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Pn=function(n,t){return zn(n,t).map(function(n){var e=Q.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Un=function(n,e){return-1!==n.indexOf(e)},Gn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,$n=function(e){return function(n){return Un(n,e)}},Wn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Un(n,"edge/")&&Un(n,"chrome")&&Un(n,"safari")&&Un(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Gn],search:function(n){return Un(n,"chrome")&&!Un(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Un(n,"msie")||Un(n,"trident")}},{name:"Opera",versionRegexes:[Gn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:$n("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:$n("firefox")},{name:"Safari",versionRegexes:[Gn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Un(n,"safari")||Un(n,"mobile/"))&&Un(n,"applewebkit")}}],_n=[{name:"Windows",search:$n("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Un(n,"iphone")||Un(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:$n("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:$n("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:$n("linux"),versionRegexes:[]},{name:"Solaris",search:$n("sunos"),versionRegexes:[]},{name:"FreeBSD",search:$n("freebsd"),versionRegexes:[]}],qn={browsers:h(Wn),oses:h(_n)},Xn=function(n){var e,t,r,o,i,u,a,c,f,s,l,d=qn.browsers(),m=qn.oses(),g=Ln(d,n).fold(un.unknown,un.nu),p=Pn(m,n).fold(mn.unknown,mn.nu);return{browser:g,os:p,deviceType:(t=g,r=n,o=(e=p).isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,c=o||u||a&&!0===/mobile/i.test(r),f=e.isiOS()||e.isAndroid(),s=f&&!c,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),{isiPad:h(o),isiPhone:h(i),isTablet:h(c),isPhone:h(s),isTouch:h(f),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:h(l)})}},Yn={detect:X(function(){var n=v.navigator.userAgent;return Xn(n)})},Kn={tap:h("alloy.tap")},Jn=h("alloy.focus"),Qn=h("alloy.blur.post"),Zn=h("alloy.paste.post"),ne=h("alloy.receive"),ee=h("alloy.execute"),te=h("alloy.focus.item"),re=Kn.tap,oe=Yn.detect().deviceType.isTouch()?Kn.tap:W,ie=h("alloy.longpress"),ue=h("alloy.system.init"),ae=h("alloy.system.attached"),ce=h("alloy.system.detached"),fe=h("alloy.focusmanager.shifted"),se=h("alloy.highlight"),le=h("alloy.dehighlight"),de=function(n,e){ve(n,n.element(),e,{})},me=function(n,e,t){ve(n,n.element(),e,t)},ge=function(n){de(n,ee())},pe=function(n,e,t){ve(n,e,t,{})},ve=function(n,e,t,r){var o=y({target:e},r);n.getSystem().triggerEvent(t,e,A(o,h))},he=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:h(n)}},be={fromHtml:function(n,e){var t=(e||v.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return he(t.childNodes[0])},fromTag:function(n,e){var t=(e||v.document).createElement(n);return he(t)},fromText:function(n,e){var t=(e||v.document).createTextNode(n);return he(t)},fromDom:he,fromPoint:function(n,e,t){var r=n.dom();return E.from(r.elementFromPoint(e,t)).map(he)}},ye=(v.Node.ATTRIBUTE_NODE,v.Node.CDATA_SECTION_NODE,v.Node.COMMENT_NODE,v.Node.DOCUMENT_NODE),xe=(v.Node.DOCUMENT_TYPE_NODE,v.Node.DOCUMENT_FRAGMENT_NODE,v.Node.ELEMENT_NODE),we=v.Node.TEXT_NODE,Se=(v.Node.PROCESSING_INSTRUCTION_NODE,v.Node.ENTITY_REFERENCE_NODE,v.Node.ENTITY_NODE,v.Node.NOTATION_NODE,function(n){return n.dom().nodeName.toLowerCase()}),Oe=function(e){return function(n){return n.dom().nodeType===e}},Te=Oe(xe),ke=Oe(we),Ee=function(n){var e=ke(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)},Ce=X(function(){return De(be.fromDom(v.document))}),De=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return be.fromDom(e)},Me=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return kn(e,function(n,e){r[n]=h(t[e])}),r}},Ae=function(n){return n.slice(0).sort()},Ie=function(e,n){if(!hn(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");kn(n,function(n){if(!pn(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})},Be=function(o,i){var t,u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Ie("required",o),Ie("optional",i),t=Ae(u),Mn(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=C(e);Vn(o,function(n){return Sn(t,n)})||function(n,e){throw new Error("All required keys ("+Ae(n).join(", ")+") were not specified. Specified keys were: "+Ae(e).join(", ")+".")}(o,t);var n=En(t,function(n){return!Sn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+Ae(n).join(", "))}(n);var r={};return kn(o,function(n){r[n]=h(e[n])}),kn(i,function(n){r[n]=h(Object.prototype.hasOwnProperty.call(e,n)?E.some(e[n]):E.none())}),r}},Re="undefined"!=typeof window?window:Function("return this;")(),Fe=function(n,e){return function(n,e){for(var t=e!==undefined&&null!==e?e:Re,r=0;r<n.length&&t!==undefined&&null!==t;++r)t=t[n[r]];return t}(n.split("."),e)},Ve={getOrDie:function(n,e){var t=Fe(n,e);if(t===undefined||null===t)throw n+" not available on this browser";return t}},Ne=xe,He=ye,je=function(n,e){var t=n.dom();if(t.nodeType!==Ne)return!1;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},ze=function(n){return n.nodeType!==Ne&&n.nodeType!==He||0===n.childElementCount},Le=function(n,e){var t=e===undefined?v.document:e.dom();return ze(t)?[]:Tn(t.querySelectorAll(n),be.fromDom)},Pe=function(n,e){var t=e===undefined?v.document:e.dom();return ze(t)?E.none():E.from(t.querySelector(n)).map(be.fromDom)},Ue=function(n,e){return n.dom()===e.dom()},Ge=(Yn.detect().browser.isIE(),function(n){return be.fromDom(n.dom().ownerDocument)}),$e=function(n){var e=n.dom();return E.from(e.parentNode).map(be.fromDom)},We=function(n){var e=n.dom();return Tn(e.childNodes,be.fromDom)},_e=function(n,e){var t=n.dom().childNodes;return E.from(t[e]).map(be.fromDom)},qe=(Me("element","offset"),function(e,t){$e(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}),Xe=function(n,e){var t;(t=n.dom(),E.from(t.nextSibling).map(be.fromDom)).fold(function(){$e(n).each(function(n){Ke(n,e)})},function(n){qe(n,e)})},Ye=function(e,t){_e(e,0).fold(function(){Ke(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},Ke=function(n,e){n.dom().appendChild(e.dom())},Je=function(e,n){kn(n,function(n){Ke(e,n)})},Qe=function(n){n.dom().textContent="",kn(We(n),function(n){Ze(n)})},Ze=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},nt=function(n){de(n,ce());var e=n.components();kn(e,nt)},et=function(n){var e=n.components();kn(e,et),de(n,ae())},tt=function(n,e){rt(n,e,Ke)},rt=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),Ee(n.element())&&et(e),n.syncComponents()},ot=function(n){nt(n),Ze(n.element()),n.getSystem().removeFromWorld(n)},it=function(e){var n=$e(e.element()).bind(function(n){return e.getSystem().getByDom(n).fold(E.none,E.some)});ot(e),n.each(function(n){n.syncComponents()})},ut=function(n,e,t){t(n,e.element());var r=We(e.element());kn(r,function(n){e.getByDom(n).each(et)})},at=function(t){return{is:function(n){return t===n},isValue:m,isError:d,getOr:h(t),getOrThunk:h(t),getOrDie:h(t),or:function(n){return at(t)},orThunk:function(n){return at(t)},fold:function(n,e){return e(t)},map:function(n){return at(n(t))},mapError:function(n){return at(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return E.some(t)}}},ct=function(t){return{is:d,isValue:d,isError:m,getOr:b,getOrThunk:function(n){return n()},getOrDie:function(){return f(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return ct(t)},mapError:function(n){return ct(n(t))},each:x,bind:function(n){return ct(t)},exists:d,forall:m,toOption:E.none}},ft={value:at,error:ct},st=function(u){if(!hn(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],t={};return kn(u,function(n,r){var e=C(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!hn(i))throw new Error("case arguments must be an array");a.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=C(n);if(a.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+e.join(","));if(!Vn(a,function(n){return Sn(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+a.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:a,constructor:o,params:t})}}}}),t},lt=Object.prototype.hasOwnProperty,dt=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)lt.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},mt=dt(function(n,e){return vn(n)&&vn(e)?mt(n,e):e}),gt=dt(function(n,e){return e}),pt=st([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),vt=function(n){return pt.defaultedThunk(h(n))},ht=pt.strict,bt=pt.asOption,yt=pt.defaultedThunk,xt=(pt.asDefaultedOptionThunk,pt.mergeWithThunk),wt=function(e){return function(n){return R(n,e)?E.from(n[e]):E.none()}},St=function(n,e){return wt(e)(n)},Ot=function(n,e){var t={};return t[n]=e,t},Tt=(st([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return wt(n)}),kt=function(n,e){return t=n,r=e,function(n){return R(n,t)?n[t]:r};var t,r},Et=function(n,e){return St(n,e)},Ct=function(n,e){return Ot(n,e)},Dt=function(n){return e={},kn(n,function(n){e[n.key]=n.value}),e;var e},Mt=function(n,e){var t,r,o,i,u,a=(t=[],r=[],kn(n,function(n){n.fold(function(n){t.push(n)},function(n){r.push(n)})}),{errors:t,values:r});return 0<a.errors.length?(u=a.errors,p(ft.error,Rn)(u)):(i=e,0===(o=a.values).length?ft.value(i):ft.value(mt(i,gt.apply(undefined,o))))},At=function(n,e){return R(t=n,r=e)&&t[r]!==undefined&&null!==t[r];var t,r};(a=u||(u={}))[a.Error=0]="Error",a[a.Value=1]="Value";var It,Bt,Rt=function(n,e,t){return n.stype===u.Error?e(n.serror):t(n.svalue)},Ft=function(n){return{stype:u.Value,svalue:n}},Vt=function(n){return{stype:u.Error,serror:n}},Nt=function(n){return n.fold(Vt,Ft)},Ht=function(n){return Rt(n,ft.error,ft.value)},jt=Ft,zt=function(n){var e=[],t=[];return kn(n,function(n){Rt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},Lt=Vt,Pt=function(n,e){return n.stype===u.Value?e(n.svalue):n},Ut=function(n,e){return n.stype===u.Error?e(n.serror):n},Gt=function(n,e){return n.stype===u.Value?{stype:u.Value,svalue:e(n.svalue)}:n},$t=function(n,e){return n.stype===u.Error?{stype:u.Error,serror:e(n.serror)}:n},Wt=function(n){return p(Lt,Rn)(n)},_t=function(n,e){var t,r,o=zt(n);return 0<o.errors.length?Wt(o.errors):(t=o.values,r=e,0<t.length?jt(mt(r,gt.apply(undefined,t))):jt(r))},qt=function(n){var e=zt(n);return 0<e.errors.length?Wt(e.errors):jt(e.values)},Xt=st([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),Yt=st([{field:["name","presence","type"]},{state:["name"]}]),Kt=function(){return Ve.getOrDie("JSON")},Jt=function(n,e,t){return Kt().stringify(n,e,t)},Qt=function(n){return vn(n)&&100<C(n).length?" removed due to size":Jt(n,null,2)},Zt=function(n,e){return Lt([{path:n,getErrorInfo:e}])},nr=st([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),er=function(t,r,o){return St(r,o).fold(function(){return n=o,e=r,Zt(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+Qt(e)});var n,e},jt)},tr=function(n,e,t){var r=St(n,e).fold(function(){return t(n)},b);return jt(r)},rr=function(a,c,n,f){return n.fold(function(o,t,n,r){var i=function(n){var e=r.extract(a.concat([o]),f,n);return Gt(e,function(n){return Ot(t,f(n))})},u=function(n){return n.fold(function(){var n=Ot(t,f(E.none()));return jt(n)},function(n){var e=r.extract(a.concat([o]),f,n);return Gt(e,function(n){return Ot(t,f(E.some(n)))})})};return n.fold(function(){return Pt(er(a,c,o),i)},function(n){return Pt(tr(c,o,n),i)},function(){return Pt(jt(St(c,o)),u)},function(n){return Pt((t=n,r=St(e=c,o).map(function(n){return!0===n?t(e):n}),jt(r)),u);var e,t,r},function(n){var e=n(c),t=Gt(tr(c,o,h({})),function(n){return mt(e,n)});return Pt(t,i)})},function(n,e){var t=e(c);return jt(Ot(n,f(t)))})},or=function(r){return{extract:function(t,n,e){return Ut(r(e,n),function(n){return e=n,Zt(t,function(){return e});var e})},toString:function(){return"val"},toDsl:function(){return Xt.itemOf(r)}}},ir=function(n){var c=ur(n),f=Cn(n,function(e,n){return n.fold(function(n){return mt(e,Ct(n,!0))},h(e))},{});return{extract:function(n,e,t){var r,o,i,u=bn(t)?[]:(o=C(r=t),En(o,function(n){return At(r,n)})),a=En(u,function(n){return!At(f,n)});return 0===a.length?c.extract(n,e,t):(i=a,Zt(n,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:c.toString,toDsl:c.toDsl}},ur=function(a){return{extract:function(n,e,t){return r=n,o=t,i=e,u=Tn(a,function(n){return rr(r,o,n,i)}),_t(u,{});var r,o,i,u},toString:function(){return"obj{\n"+Tn(a,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return Xt.objOf(Tn(a,function(n){return n.fold(function(n,e,t,r){return Yt.field(n,t,r)},function(n,e){return Yt.state(n)})}))}}},ar=function(t,i){var u=function(n,e){return(o=or(t),{extract:function(t,r,n){var e=Tn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return qt(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return Xt.arrOf(o)}}).extract(n,b,e);var o};return{extract:function(t,r,o){var n=C(o),e=u(t,n);return Pt(e,function(n){var e=Tn(n,function(n){return nr.field(n,n,ht(),i)});return ur(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return Xt.setOf(t,i)}}},cr=h(or(jt)),fr=nr.state,sr=nr.field,lr=function(t,e,r,o,i){return Et(o,i).fold(function(){return n=o,e=i,Zt(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Qt(n)});var n,e},function(n){return ur(n).extract(t.concat(["branch: "+i]),e,r)})},dr=function(o,i){return{extract:function(e,t,r){return Et(r,o).fold(function(){return n=o,Zt(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return lr(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+C(i)},toDsl:function(){return Xt.choiceOf(o,i)}}},mr=or(jt),gr=function(e){return or(function(n){return e(n).fold(Lt,jt)})},pr=function(e,n){return ar(function(n){return Nt(e(n))},n)},vr=function(n,e,t){return Ht((r=n,o=b,i=t,u=e.extract([r],o,i),$t(u,function(n){return{input:i,errors:n}})));var r,o,i,u},hr=function(n){return n.fold(function(n){throw new Error(yr(n))},b)},br=function(n,e,t){return hr(vr(n,e,t))},yr=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,Tn(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}))+"\n\nInput object: "+Qt(n.input);var e,t},xr=function(n,e){return dr(n,e)},wr=h(mr),Sr=(It=yn,Bt="function",or(function(n){var e=typeof n;return It(n)?jt(n):Lt("Expected type: "+Bt+" but got: "+e)})),Or=function(n){return sr(n,n,ht(),cr())},Tr=function(n,e){return sr(n,n,ht(),e)},kr=function(n,e){return sr(n,n,ht(),ur(e))},Er=function(n){return sr(n,n,bt(),cr())},Cr=function(n,e){return sr(n,n,bt(),ur(e))},Dr=function(n,e){return sr(n,n,bt(),ir(e))},Mr=function(n,e){return sr(n,n,vt(e),cr())},Ar=function(n,e,t){return sr(n,n,vt(e),t)},Ir=function(n,e){return fr(n,e)},Br=function(n,e){return Ue(n.element(),e.event().target())},Rr=function(n){if(!At(n,"can")&&!At(n,"abort")&&!At(n,"run"))throw new Error("EventHandler defined by: "+Jt(n,null,2)+" does not have can, abort, or run!");return br("Extracting event.handler",ir([Mr("can",h(!0)),Mr("abort",h(!1)),Mr("run",x)]),n)},Fr=function(t){var e,r,o,i,n=(e=t,r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Dn(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Dn(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return Rr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];kn(t,function(n){n.run.apply(undefined,e)})}})};function Vr(n,e,t,r,o){return n(t,r)?E.some(t):yn(o)&&o(t)?E.none():e(t,r,o)}var Nr,Hr,jr,zr=function(n,e,t){for(var r=n.dom(),o=yn(t)?t:h(!1);r.parentNode;){r=r.parentNode;var i=be.fromDom(r);if(e(i))return E.some(i);if(o(i))break}return E.none()},Lr=function(n,e,t){return Vr(function(n){return e(n)},zr,n,e,t)},Pr=function(n,r){var o=function(n){for(var e=0;e<n.childNodes.length;e++){if(r(be.fromDom(n.childNodes[e])))return E.some(be.fromDom(n.childNodes[e]));var t=o(n.childNodes[e]);if(t.isSome())return t}return E.none()};return o(n.dom())},Ur=function(n,e,t){return Lr(n,function(n){return e(n).isSome()},t).bind(e)},Gr=function(n){return Dt(n)},$r=function(n,e){return{key:n,value:Rr({abort:e})}},Wr=function(n,e){return{key:n,value:Rr({run:e})}},_r=function(n,e,t){return{key:n,value:Rr({run:function(n){e.apply(undefined,[n].concat(t))}})}},qr=function(n){return function(t){return{key:n,value:Rr({run:function(n,e){Br(n,e)&&t(n,e)}})}}},Xr=function(n,e,t){var u,r,o=e.partUids[t];return r=o,Wr(u=n,function(n,i){n.getSystem().getByUid(r).each(function(n){var e,t,r,o;t=(e=n).element(),r=u,o=i,e.getSystem().triggerEvent(r,t,o.event())})})},Yr=function(n){return Wr(n,function(n,e){e.cut()})},Kr=qr(ae()),Jr=qr(ce()),Qr=qr(ue()),Zr=(Nr=ee(),function(n){return Wr(Nr,n)}),no=function(n){return Tn(n,function(n){return r=e="/*",o=(t=n).length-e.length,""!==r&&(t.length<r.length||t.substr(o,o+r.length)!==r)?n:n.substring(0,n.length-"/*".length);var e,t,r,o})},eo=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:no(i)}},n},to=function(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}},ro=function(t,r,o){return Qr(function(n,e){o(n,t,r)})},oo=function(n,e,t,r,o,i){var u,a,c=n,f=Cr(e,[(u="config",a=n,sr(u,u,bt(),a))]);return ao(c,f,e,t,r,o,i)},io=function(o,i,u){var n,e,t,r,a,c;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:h(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,a=t.indexOf("("),c=t.substring(a+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:no(c.slice(0,1).concat(c.slice(3)))}},n},uo=function(n){return{key:n,value:undefined}},ao=function(t,n,r,o,e,i,u){var a=function(n){return At(n,r)?n[r]():E.none()},c=A(e,function(n,e){return io(r,n,e)}),f=A(i,function(n,e){return eo(n,e)}),s=y({},f,c,{revoke:l(uo,r),config:function(n){var e=br(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:X(function(){return br(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return a(n).bind(function(e){return Et(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(to({}))},name:function(){return r},handlers:function(n){return a(n).map(function(n){return kt("events",function(n,e){return{}})(o)(n.config,n.state)}).getOr({})}});return s},co={init:function(){return fo({readState:function(){return"No State required"}})}},fo=function(n){return n},so=function(n){return Dt(n)},lo=ir([Or("fields"),Or("name"),Mr("active",{}),Mr("apis",{}),Mr("state",co),Mr("extra",{})]),mo=function(n){var e,t,r,o,i,u,a,c,f=br("Creating behaviour: "+n.name,lo,n);return e=f.fields,t=f.name,r=f.active,o=f.apis,i=f.extra,u=f.state,a=ir(e),c=Cr(t,[Dr("config",e)]),ao(a,c,t,r,o,i,u)},go=ir([Or("branchKey"),Or("branches"),Or("name"),Mr("active",{}),Mr("apis",{}),Mr("state",co),Mr("extra",{})]),po=h(undefined),vo=function(n,e,t){if(!(pn(t)||bn(t)||xn(t)))throw v.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},ho=function(n,e,t){vo(n.dom(),e,t)},bo=function(n,e){var t=n.dom();M(e,function(n,e){vo(t,e,n)})},yo=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},xo=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},wo=function(n,e){n.dom().removeAttribute(e)},So=function(n,e){var t=yo(n,e);return t===undefined||""===t?[]:t.split(" ")},Oo=function(n){return n.dom().classList!==undefined},To=function(n,e){return o=e,i=So(t=n,r="class").concat([o]),ho(t,r,i.join(" ")),!0;var t,r,o,i},ko=function(n,e){return o=e,0<(i=En(So(t=n,r="class"),function(n){return n!==o})).length?ho(t,r,i.join(" ")):wo(t,r),!1;var t,r,o,i},Eo=function(n,e){Oo(n)?n.dom().classList.add(e):To(n,e)},Co=function(n){0===(Oo(n)?n.dom().classList:So(n,"class")).length&&wo(n,"class")},Do=function(n,e){Oo(n)?n.dom().classList.remove(e):ko(n,e),Co(n)},Mo=function(n,e){return Oo(n)&&n.dom().classList.contains(e)},Ao=function(n,e,t){Do(n,t),Eo(n,e)},Io=/* */Object.freeze({toAlpha:function(n,e,t){Ao(n.element(),e.alpha,e.omega)},toOmega:function(n,e,t){Ao(n.element(),e.omega,e.alpha)},isAlpha:function(n,e,t){return Mo(n.element(),e.alpha)},isOmega:function(n,e,t){return Mo(n.element(),e.omega)},clear:function(n,e,t){Do(n.element(),e.alpha),Do(n.element(),e.omega)}}),Bo=[Or("alpha"),Or("omega")],Ro=mo({fields:Bo,name:"swapping",apis:Io}),Fo=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return Fo(t())}}},Vo=function(n){n.dom().focus()},No=function(n){n.dom().blur()},Ho=function(n){var e=n!==undefined?n.dom():v.document;return E.from(e.activeElement).map(be.fromDom)},jo=function(e){return Ho(Ge(e)).filter(function(n){return e.dom().contains(n.dom())})},zo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Lo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Po=function(n){var e=v.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=v.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,v.window,0,0,0,0,0,!1,!1,!1,!1,0,null),v.document.body.appendChild(e),e.dispatchEvent(t),v.document.body.removeChild(e)},Uo={formatChanged:h("formatChanged"),orientationChanged:h("orientationChanged"),dropupDismissed:h("dropupDismissed")},Go=function(n){return n.dom().innerHTML},$o=function(n,e){var t,r,o=Ge(n).dom(),i=be.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||v.document).createElement("div")).innerHTML=t,We(be.fromDom(r)));Je(i,u),Qe(n),Ke(n,i)},Wo=function(n){return e=n,t=!1,be.fromDom(e.dom().cloneNode(t));var e,t},_o=function(n){var e,t,r,o=Wo(n);return e=o,t=be.fromTag("div"),r=be.fromDom(e.dom().cloneNode(!0)),Ke(t,r),Go(t)},qo=function(n){return _o(n)},Xo=/* */Object.freeze({events:function(a){return Gr([Wr(ne(),function(o,i){var n,e,u=a.channels,t=C(u),r=(n=t,(e=i).universal()?n:En(n,function(n){return Sn(e.channels(),n)}));kn(r,function(n){var e=u[n],t=e.schema,r=br("channel["+n+"] data\nReceiver: "+qo(o.element()),t,i.data());e.onReceive(o,r)})})])}}),Yo=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Ko=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return E.none()},Jo="unknown";(jr=Hr||(Hr={}))[jr.STOP=0]="STOP",jr[jr.NORMAL=1]="NORMAL",jr[jr.LOGGING=2]="LOGGING";var Qo=Fo({}),Zo=function(e,n,t){var r,o,i,u;switch(Et(Qo.get(),e).orThunk(function(){var n=C(Qo.get());return Ko(n,function(n){return-1<e.indexOf(n)?E.some(Qo.get()[n]):E.none()})}).getOr(Hr.NORMAL)){case Hr.NORMAL:return t(ti());case Hr.LOGGING:var a=(r=e,o=n,i=[],u=(new Date).getTime(),{logEventCut:function(n,e,t){i.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){i.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){i.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){i.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();Sn(["mousemove","mouseover","mouseout",ue()],r)||v.console.log(r,{event:r,time:n-u,target:o.dom(),sequence:Tn(i,function(n){return Sn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+qo(n.target)+")":n.outcome})})}}),c=t(a);return a.write(),c;case Hr.STOP:return!0}},ni=["alloy/data/Fields","alloy/debugging/Debugging"],ei=function(n,e,t){return Zo(n,e,t)},ti=h({logEventCut:x,logEventStopped:x,logNoParent:x,logEventNoHandlers:x,logEventResponse:x,write:x}),ri=h([Or("menu"),Or("selectedMenu")]),oi=h([Or("item"),Or("selectedItem")]),ii=(h(ur(oi().concat(ri()))),h(ur(oi()))),ui=kr("initSize",[Or("numColumns"),Or("numRows")]),ai=function(n,e,t){return function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");Mn(e,function(e){return 0<e.indexOf("alloy")&&!On(ni,function(n){return-1<e.indexOf(n)})}).getOr(Jo)}}(),sr(e,e,t,gr(function(t){return ft.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))},ci=function(n){return ai(0,n,vt(x))},fi=function(n){return ai(0,n,vt(E.none))},si=function(n){return ai(0,n,ht())},li=function(n){return ai(0,n,ht())},di=function(n,e){return Ir(n,h(e))},mi=function(n){return Ir(n,b)},gi=h(ui),pi=[Tr("channels",pr(ft.value,ir([si("onReceive"),Mr("schema",wr())])))],vi=mo({fields:pi,name:"receiving",active:Xo}),hi=function(n,e,t){var r=e.aria;r.update(n,r,t.get())},bi=function(e,n,t){n.toggleClass.each(function(n){t.get()?Eo(e.element(),n):Do(e.element(),n)})},yi=function(n,e,t){Si(n,e,t,!t.get())},xi=function(n,e,t){t.set(!0),bi(n,e,t),hi(n,e,t)},wi=function(n,e,t){t.set(!1),bi(n,e,t),hi(n,e,t)},Si=function(n,e,t,r){(r?xi:wi)(n,e,t)},Oi=function(n,e,t){Si(n,e,t,e.selected)},Ti=/* */Object.freeze({onLoad:Oi,toggle:yi,isOn:function(n,e,t){return t.get()},on:xi,off:wi,set:Si}),ki=/* */Object.freeze({exhibit:function(n,e,t){return to({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=yi,Zr(function(n){o(n,t,r)})),u=ro(n,e,Oi);return Gr(Rn([n.toggleOnExecute?[i]:[],[u]]))}}),Ei=/* */Object.freeze({init:function(n){var e=Fo(!1);return{readState:function(){return e.get()},get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(!1)}}}}),Ci=function(n,e,t){ho(n.element(),"aria-expanded",t)},Di=[Mr("selected",!1),Er("toggleClass"),Mr("toggleOnExecute",!0),Ar("aria",{mode:"none"},xr("mode",{pressed:[Mr("syncWithExpanded",!1),di("update",function(n,e,t){ho(n.element(),"aria-pressed",t),e.syncWithExpanded&&Ci(n,e,t)})],checked:[di("update",function(n,e,t){ho(n.element(),"aria-checked",t)})],expanded:[di("update",Ci)],selected:[di("update",function(n,e,t){ho(n.element(),"aria-selected",t)})],none:[di("update",x)]}))],Mi=mo({fields:Di,name:"toggling",active:ki,apis:Ti,state:Ei}),Ai=function(t,r){return vi.config({channels:Ct(Uo.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},Ii=function(n){return vi.config({channels:Ct(Uo.orientationChanged(),{onReceive:n})})},Bi=function(n,e){return{key:n,value:{onReceive:e}}},Ri="tinymce-mobile",Fi={resolve:function(n){return Ri+"-"+n},prefix:h(Ri)},Vi=function(n,e){e.ignore||(Vo(n.element()),e.onFocus(n))},Ni=/* */Object.freeze({focus:Vi,blur:function(n,e){e.ignore||No(n.element())},isFocused:function(n){return e=n.element(),t=Ge(e).dom(),e.dom()===t.activeElement;var e,t}}),Hi=/* */Object.freeze({exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return to(t)},events:function(t){return Gr([Wr(Jn(),function(n,e){Vi(n,t),e.stop()})].concat(t.stopMousedown?[Wr(H(),function(n,e){e.event().prevent()})]:[]))}}),ji=[ci("onFocus"),Mr("stopMousedown",!1),Mr("ignore",!1)],zi=mo({fields:ji,name:"focusing",active:Hi,apis:Ni}),Li=function(n){return n.style!==undefined},Pi=function(n,e,t){if(!pn(t))throw v.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Li(n)&&n.style.setProperty(e,t)},Ui=function(n,e,t){var r=n.dom();Pi(r,e,t)},Gi=function(n,e){var t=n.dom();M(e,function(n,e){Pi(t,e,n)})},$i=function(n,e){var t=n.dom(),r=v.window.getComputedStyle(t).getPropertyValue(e),o=""!==r||Ee(n)?r:Wi(t,e);return null===o?undefined:o},Wi=function(n,e){return Li(n)?n.style.getPropertyValue(e):""},_i=function(n,e){var t=n.dom(),r=Wi(t,e);return E.from(r).filter(function(n){return 0<n.length})},qi=function(n,e){var t,r,o=n.dom();r=e,Li(t=o)&&t.style.removeProperty(r),xo(n,"style")&&""===yo(n,"style").replace(/^\s+|\s+$/g,"")&&wo(n,"style")},Xi=function(n){return n.dom().offsetWidth};function Yi(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=$i(n,r);return parseFloat(t)||0}return e},i=function(o,n){return Dn(n,function(n,e){var t=$i(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!xn(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();Li(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var Ki,Ji,Qi=Yi("height",function(n){var e=n.dom();return Ee(n)?e.getBoundingClientRect().height:e.offsetHeight}),Zi=function(n){return Qi.get(n)},nu=function(n,e,t){return En(function(n,e){for(var t=yn(e)?e:h(!1),r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=be.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},eu=function(n,e){return En($e(t=n).map(We).map(function(n){return En(n,function(n){return!Ue(t,n)})}).getOr([]),e);var t},tu=function(n,e){return Le(e,n)},ru=function(n){return Pe(n)},ou=function(n,e,t){return zr(n,function(n){return je(n,e)},t)},iu=function(n,e){return Pe(e,n)},uu=function(n,e,t){return Vr(je,ou,n,e,t)},au=function(n,e,t){var r=Hn(n.slice(0,e)),o=Hn(n.slice(e+1));return Mn(r.concat(o),t)},cu=function(n,e,t){var r=Hn(n.slice(0,e));return Mn(r,t)},fu=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return Mn(o.concat(r),t)},su=function(n,e,t){var r=n.slice(e+1);return Mn(r,t)},lu=function(t){return function(n){var e=n.raw();return Sn(t,e.which)}},du=function(n){return function(e){return Vn(n,function(n){return n(e)})}},mu=function(n){return!0===n.raw().shiftKey},gu=function(n){return!0===n.raw().ctrlKey},pu=S(mu),vu=function(n,e){return{matches:n,classification:e}},hu=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},bu=function(n,e,t){return n<=e?e:t<=n?t:n},yu=function(t,r,n,o){var e=tu(t.element(),"."+r.highlightClass);kn(e,function(e){On(o,function(n){return n.element()===e})||(Do(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),de(n,le())}))})},xu=function(n,e,t,r){yu(n,e,0,[r]),wu(n,e,t,r)||(Eo(r.element(),e.highlightClass),e.onHighlight(n,r),de(r,se()))},wu=function(n,e,t,r){return Mo(r.element(),e.highlightClass)},Su=function(n,e,t,r){var o=tu(n.element(),"."+e.itemClass);return E.from(o[r]).fold(function(){return ft.error("No element found with index "+r)},n.getSystem().getByDom)},Ou=function(e,n,t){return iu(e.element(),"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Tu=function(e,n,t){var r=tu(e.element(),"."+n.itemClass);return(0<r.length?E.some(r[r.length-1]):E.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ku=function(t,e,n,r){var o=tu(t.element(),"."+e.itemClass);return An(o,function(n){return Mo(n,e.highlightClass)}).bind(function(n){var e=hu(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})},Eu=function(e,n,t){var r=tu(e.element(),"."+n.itemClass);return Yo(Tn(r,function(n){return e.getSystem().getByDom(n).toOption()}))},Cu=/* */Object.freeze({dehighlightAll:function(n,e,t){return yu(n,e,0,[])},dehighlight:function(n,e,t,r){wu(n,e,t,r)&&(Do(r.element(),e.highlightClass),e.onDehighlight(n,r),de(r,le()))},highlight:xu,highlightFirst:function(e,t,r){Ou(e,t,r).each(function(n){xu(e,t,r,n)})},highlightLast:function(e,t,r){Tu(e,t,r).each(function(n){xu(e,t,r,n)})},highlightAt:function(e,t,r,n){Su(e,t,r,n).fold(function(n){throw new Error(n)},function(n){xu(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Eu(e,t,r);Mn(o,n).each(function(n){xu(e,t,r,n)})},isHighlighted:wu,getHighlighted:function(e,n,t){return iu(e.element(),"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:Ou,getLast:Tu,getPrevious:function(n,e,t){return ku(n,e,0,-1)},getNext:function(n,e,t){return ku(n,e,0,1)},getCandidates:Eu}),Du=[Or("highlightClass"),Or("itemClass"),ci("onHighlight"),ci("onDehighlight")],Mu=mo({fields:Du,name:"highlighting",apis:Cu}),Au=function(n,e,t){e.exists(function(e){return t.exists(function(n){return Ue(n,e)})})||me(n,fe(),{prevFocus:e,newFocus:t})},Iu=function(){var o=function(n){return jo(n.element())};return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element());var r=o(n);Au(n,t,r)}}};(Ji=Ki||(Ki={})).OnFocusMode="onFocus",Ji.OnEnterOrSpaceMode="onEnterOrSpace",Ji.OnApiMode="onApi";var Bu,Ru,Fu=function(n,e,t,r,a){var c=function(e,t,n,r,o){var i,u,a=n(e,t,r,o);return(i=a,u=t.event(),Mn(i,function(n){return n.matches(u)}).map(function(n){return n.classification})).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([Mr("focusManager",Iu()),Ar("focusInside","onFocus",gr(function(n){return Sn(["onFocus","onEnterOrSpace","onApi"],n)?ft.value(n):ft.error("Invalid value for focusInside")})),di("handler",o),di("state",e),di("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var n=i.focusInside!==Ki.OnFocusMode?E.none():a(i).map(function(t){return Wr(Jn(),function(n,e){t(n,i,u),e.stop()})});return Gr(n.toArray().concat([Wr(P(),function(r,o){c(r,o,t,i,u).fold(function(){var e,t,n;e=r,t=o,n=lu([32].concat([13]))(t.event()),i.focusInside===Ki.OnEnterOrSpaceMode&&n&&Br(e,t)&&a(i).each(function(n){n(e,i,u),t.stop()})},function(n){o.stop()})}),Wr(U(),function(n,e){c(n,e,r,i,u).each(function(n){e.stop()})})]))}};return o},Vu=function(n){var e=[Er("onEscape"),Er("onEnter"),Mr("selector",'[data-alloy-tabstop="true"]'),Mr("firstTabstop",0),Mr("useTabstopAt",h(!0)),Er("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector.bind(function(n){return uu(e,n)}).getOr(e);return 0<Zi(t)},t=function(e,t){var n,r,o,i;(n=e,r=t,o=tu(n.element(),r.selector),i=En(o,function(n){return u(r,n)}),E.from(i[r.firstTabstop])).each(function(n){t.focusManager.set(e,n)})},a=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt(t);var e,t}).fold(function(){return r.cyclic?E.some(!0):E.none()},function(n){return r.focusManager.set(e,n),E.some(!0)})},i=function(e,n,t,r){var o,i,u=tu(e.element(),t.selector);return(o=e,i=t,i.focusManager.get(o).bind(function(n){return uu(n,i.selector)})).bind(function(n){return An(u,l(Ue,n)).bind(function(n){return a(e,u,n,t,r)})})},r=h([vu(du([mu,lu([9])]),function(n,e,t,r){var o=t.cyclic?au:cu;return i(n,0,t,o)}),vu(lu([9]),function(n,e,t,r){var o=t.cyclic?fu:su;return i(n,0,t,o)}),vu(lu([27]),function(e,t,n,r){return n.onEscape.bind(function(n){return n(e,t)})}),vu(du([pu,lu([13])]),function(e,t,n,r){return n.onEnter.bind(function(n){return n(e,t)})})]),o=h([]);return Fu(e,co.init,r,o,function(){return E.some(t)})},Nu=Vu(Ir("cyclic",h(!1))),Hu=Vu(Ir("cyclic",h(!0))),ju=function(n){return"input"===Se(n)&&"radio"!==yo(n,"type")||"textarea"===Se(n)},zu=function(n,e,t){return ju(t)&&lu([32])(e.event())?E.none():(pe(n,t,ee()),E.some(!0))},Lu=function(n,e){return E.some(!0)},Pu=[Mr("execute",zu),Mr("useSpace",!1),Mr("useEnter",!0),Mr("useControlEnter",!1),Mr("useDown",!1)],Uu=function(n,e,t){return t.execute(n,e,n.element())},Gu=Fu(Pu,co.init,function(n,e,t,r){var o=t.useSpace&&!ju(n.element())?[32]:[],i=t.useEnter?[13]:[],u=t.useDown?[40]:[],a=o.concat(i).concat(u);return[vu(lu(a),Uu)].concat(t.useControlEnter?[vu(du([gu,lu([13])]),Uu)]:[])},function(n,e,t,r){return t.useSpace&&!ju(n.element())?[vu(lu([32]),Lu)]:[]},function(){return E.none()}),$u=function(n){var t=Fo(E.none());return fo({readState:function(){return t.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(E.some({numRows:h(n),numColumns:h(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})},Wu=/* */Object.freeze({flatgrid:$u,init:function(n){return n.state(n)}}),_u=function(e,t){return function(n){return"rtl"===qu(n)?t:e}},qu=function(n){return"rtl"===$i(n,"direction")?"rtl":"ltr"},Xu=function(i){return function(n,e,t,r){var o=i(n.element());return Qu(o,n,e,t,r)}},Yu=function(n,e){var t=_u(n,e);return Xu(t)},Ku=function(n,e){var t=_u(e,n);return Xu(t)},Ju=function(o){return function(n,e,t,r){return Qu(o,n,e,t,r)}},Qu=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},Zu=Ju,na=Ju,ea=Ju,ta=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},ra=Be(["index","candidates"],[]),oa=function(n,e,t){return ia(n,e,t,ta)},ia=function(n,e,t,r){var o,i=l(Ue,e),u=tu(n,t),a=En(u,ta);return An(o=a,i).map(function(n){return ra({index:n,candidates:o})})},ua=function(n,e){return An(n,function(n){return Ue(e,n)})},aa=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?E.some(t[e]):E.none()})},ca=function(o,n,i,u,a){return aa(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=hu(e,a,0,t-1);return E.some({row:h(n),column:h(r)})})},fa=function(i,n,u,a,c){return aa(i,n,a,function(n,e){var t=hu(n,c,0,u-1),r=t===u-1?i.length-t*a:a,o=bu(e,0,r-1);return E.some({row:h(t),column:h(o)})})},sa=[Or("selector"),Mr("execute",zu),fi("onEscape"),Mr("captureTab",!1),gi()],la=function(e,t,n){iu(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})},da=function(o){return function(n,e,t,r){return oa(n,e,t.selector).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}},ma=function(n,e,t,r){return t.captureTab?E.some(!0):E.none()},ga=da(function(n,e,t,r){return ca(n,e,t,r,-1)}),pa=da(function(n,e,t,r){return ca(n,e,t,r,1)}),va=da(function(n,e,t,r){return fa(n,e,t,r,-1)}),ha=da(function(n,e,t,r){return fa(n,e,t,r,1)}),ba=h([vu(lu([37]),Yu(ga,pa)),vu(lu([39]),Ku(ga,pa)),vu(lu([38]),Zu(va)),vu(lu([40]),na(ha)),vu(du([mu,lu([9])]),ma),vu(du([pu,lu([9])]),ma),vu(lu([27]),function(n,e,t,r){return t.onEscape(n,e)}),vu(lu([32].concat([13])),function(e,t,r,n){return(o=e,i=r,i.focusManager.get(o).bind(function(n){return uu(n,i.selector)})).bind(function(n){return r.execute(e,t,n)});var o,i})]),ya=h([vu(lu([32]),Lu)]),xa=Fu(sa,$u,ba,ya,function(){return E.some(la)}),wa=function(n,e,t,i){var u=function(n,e,t){var r,o=hu(e,i,0,t.length-1);return o===n?E.none():(r=t[o],"button"===Se(r)&&"disabled"===yo(r,"disabled")?u(n,o,t):E.from(t[o]))};return oa(n,t,e).bind(function(n){var e=n.index(),t=n.candidates();return u(e,e,t)})},Sa=[Or("selector"),Mr("getInitial",E.none),Mr("execute",zu),fi("onEscape"),Mr("executeOnMove",!1),Mr("allowVertical",!0)],Oa=function(e,t,r){return(n=e,o=r,o.focusManager.get(n).bind(function(n){return uu(n,o.selector)})).bind(function(n){return r.execute(e,t,n)});var n,o},Ta=function(e,t){t.getInitial(e).orThunk(function(){return iu(e.element(),t.selector)}).each(function(n){t.focusManager.set(e,n)})},ka=function(n,e,t){return wa(n,t.selector,e,-1)},Ea=function(n,e,t){return wa(n,t.selector,e,1)},Ca=function(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove?Oa(n,e,t):E.some(!0)})}},Da=function(n,e,t,r){return t.onEscape(n,e)},Ma=h([vu(lu([32]),Lu)]),Aa=Fu(Sa,co.init,function(n,e,t,r){var o=[37].concat(t.allowVertical?[38]:[]),i=[39].concat(t.allowVertical?[40]:[]);return[vu(lu(o),Ca(Yu(ka,Ea))),vu(lu(i),Ca(Ku(ka,Ea))),vu(lu([13]),Oa),vu(lu([32]),Oa),vu(lu([27]),Da)]},Ma,function(){return E.some(Ta)}),Ia=Be(["rowIndex","columnIndex","cell"],[]),Ba=function(n,e,t){return E.from(n[e]).bind(function(n){return E.from(n[t]).map(function(n){return Ia({rowIndex:e,columnIndex:t,cell:n})})})},Ra=function(n,e,t,r){var o=n[e].length,i=hu(t,r,0,o-1);return Ba(n,e,i)},Fa=function(n,e,t,r){var o=hu(t,r,0,n.length-1),i=n[o].length,u=bu(e,0,i-1);return Ba(n,o,u)},Va=function(n,e,t,r){var o=n[e].length,i=bu(t+r,0,o-1);return Ba(n,e,i)},Na=function(n,e,t,r){var o=bu(t+r,0,n.length-1),i=n[o].length,u=bu(e,0,i-1);return Ba(n,o,u)},Ha=[kr("selectors",[Or("row"),Or("cell")]),Mr("cycles",!0),Mr("previousSelector",E.none),Mr("execute",zu)],ja=function(e,t){t.previousSelector(e).orThunk(function(){var n=t.selectors;return iu(e.element(),n.cell)}).each(function(n){t.focusManager.set(e,n)})},za=function(n,e){return function(t,r,i){var u=i.cycles?n:e;return uu(r,i.selectors.row).bind(function(n){var e=tu(n,i.selectors.cell);return ua(e,r).bind(function(r){var o=tu(t,i.selectors.row);return ua(o,n).bind(function(n){var e,t=(e=i,Tn(o,function(n){return tu(n,e.selectors.cell)}));return u(t,n,r).map(function(n){return n.cell()})})})})}},La=za(function(n,e,t){return Ra(n,e,t,-1)},function(n,e,t){return Va(n,e,t,-1)}),Pa=za(function(n,e,t){return Ra(n,e,t,1)},function(n,e,t){return Va(n,e,t,1)}),Ua=za(function(n,e,t){return Fa(n,t,e,-1)},function(n,e,t){return Na(n,t,e,-1)}),Ga=za(function(n,e,t){return Fa(n,t,e,1)},function(n,e,t){return Na(n,t,e,1)}),$a=h([vu(lu([37]),Yu(La,Pa)),vu(lu([39]),Ku(La,Pa)),vu(lu([38]),Zu(Ua)),vu(lu([40]),na(Ga)),vu(lu([32].concat([13])),function(e,t,r){return jo(e.element()).bind(function(n){return r.execute(e,t,n)})})]),Wa=h([vu(lu([32]),Lu)]),_a=Fu(Ha,co.init,$a,Wa,function(){return E.some(ja)}),qa=[Or("selector"),Mr("execute",zu),Mr("moveOnTab",!1)],Xa=function(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})},Ya=function(e,t){iu(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})},Ka=function(n,e,t){return wa(n,t.selector,e,-1)},Ja=function(n,e,t){return wa(n,t.selector,e,1)},Qa=h([vu(lu([38]),ea(Ka)),vu(lu([40]),ea(Ja)),vu(du([mu,lu([9])]),function(n,e,t){return t.moveOnTab?ea(Ka)(n,e,t):E.none()}),vu(du([pu,lu([9])]),function(n,e,t){return t.moveOnTab?ea(Ja)(n,e,t):E.none()}),vu(lu([13]),Xa),vu(lu([32]),Xa)]),Za=h([vu(lu([32]),Lu)]),nc=Fu(qa,co.init,Qa,Za,function(){return E.some(Ya)}),ec=[fi("onSpace"),fi("onEnter"),fi("onShiftEnter"),fi("onLeft"),fi("onRight"),fi("onTab"),fi("onShiftTab"),fi("onUp"),fi("onDown"),fi("onEscape"),Mr("stopSpaceKeyup",!1),Er("focusIn")],tc=Fu(ec,co.init,function(n,e,t){return[vu(lu([32]),t.onSpace),vu(du([pu,lu([13])]),t.onEnter),vu(du([mu,lu([13])]),t.onShiftEnter),vu(du([mu,lu([9])]),t.onShiftTab),vu(du([pu,lu([9])]),t.onTab),vu(lu([38]),t.onUp),vu(lu([40]),t.onDown),vu(lu([37]),t.onLeft),vu(lu([39]),t.onRight),vu(lu([32]),t.onSpace),vu(lu([27]),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[vu(lu([32]),Lu)]:[]},function(n){return n.focusIn}),rc=Nu.schema(),oc=Hu.schema(),ic=Aa.schema(),uc=xa.schema(),ac=_a.schema(),cc=Gu.schema(),fc=nc.schema(),sc=tc.schema(),lc=(Ru=br("Creating behaviour: "+(Bu={branchKey:"mode",branches:/* */Object.freeze({acyclic:rc,cyclic:oc,flow:ic,flatgrid:uc,matrix:ac,execution:cc,menu:fc,special:sc}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element(),e.element())},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){At(t,"setGridSize")?t.setGridSize(r,o):v.console.error("Layout does not support setGridSize")}},state:Wu}).name,go,Bu),oo(xr(Ru.branchKey,Ru.branches),Ru.name,Ru.active,Ru.apis,Ru.extra,Ru.state)),dc=function(r,n){return e=r,t={},o=Tn(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,sr(e,e,bt(),or(function(n){return Lt("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([Ir("dump",b)]),sr(e,e,vt(t),ur(o));var e,t,o},mc=function(n){return n.dump},gc=function(n,e){return y({},n.dump,so(e))},pc=dc,vc=gc,hc="placeholder",bc=st([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),yc=function(n,e,t,r){return t.uiType===hc?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?bc.single(!0,h(i)):Et(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+C(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+Jt(i,null,2))},function(n){return n.replace()})):bc.single(!1,h(t));var o,i,u},xc=function(i,u,a,c){return yc(i,0,a,c).fold(function(n,e){var t=e(u,a.config,a.validated),r=Et(t,"components").getOr([]),o=Fn(r,function(n){return xc(i,u,n,c)});return[y({},t,{components:o})]},function(n,e){var t=e(u,a.config,a.validated);return a.validated.preprocess.getOr(b)(t)})},wc=function(e,t,n,r){var o,i,u,a=A(r,function(n,e){return r=n,o=!1,{name:h(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(!0===o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),c=(o=e,i=t,u=a,Fn(n,function(n){return xc(o,i,n,u)}));return M(a,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+Jt(t.components,null,2))}),c},Sc=bc.single,Oc=bc.multiple,Tc=h(hc),kc=0,Ec=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++kc+String(e)},Cc=st([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Dc=Mr("factory",{sketch:b}),Mc=Mr("schema",[]),Ac=Or("name"),Ic=sr("pname","pname",yt(function(n){return"<alloy."+Ec(n.name)+">"}),wr()),Bc=Ir("schema",function(){return[Er("preprocess")]}),Rc=Mr("defaults",h({})),Fc=Mr("overrides",h({})),Vc=ur([Dc,Mc,Ac,Ic,Rc,Fc]),Nc=ur([Dc,Mc,Ac,Ic,Rc,Fc]),Hc=ur([Dc,Bc,Ac,Or("unit"),Ic,Rc,Fc]),jc=function(n){var e=function(n){return n.name};return n.fold(e,e,e,e)},zc=function(t,r){return function(n){var e=br("Converting part type",r,n);return t(e)}},Lc=zc(Cc.required,Vc),Pc=zc(Cc.optional,Nc),Uc=zc(Cc.group,Hc),Gc=h("entirety"),$c=function(n,e,t,r){return mt(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))},Wc=function(o,n){var i={};return kn(n,function(n){var e;(e=n,e.fold(E.some,E.none,E.some,E.some)).each(function(t){var r=_c(o,t.pname);i[t.name]=function(n){var e=br("Part: "+t.name+" in "+o,ur(t.schema),n);return y({},r,{config:n,validated:e})}})}),i},_c=function(n,e){return{uiType:Tc(),owner:n,name:e}},qc=function(n,e,t){return r=e,i={},o={},kn(t,function(n){n.fold(function(r){i[r.pname]=Sc(!0,function(n,e,t){return r.factory.sketch($c(n,r,e,t))})},function(n){var e=r.parts[n.name];o[n.name]=h(n.factory.sketch($c(r,n,e[Gc()]),e))},function(r){i[r.pname]=Sc(!1,function(n,e,t){return r.factory.sketch($c(n,r,e,t))})},function(o){i[o.pname]=Oc(!0,function(e,n,t){var r=e[o.name];return Tn(r,function(n){return o.factory.sketch(mt(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:h(i),externals:h(o)};var r,i,o},Xc=function(n,e,t){return wc(E.some(n),e,e.components,t)},Yc=function(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOption()},Kc=function(n,e,t){return Yc(n,e,t).getOrDie("Could not find part: "+t)},Jc=function(e,n){var t=Tn(n,jc);return Dt(Tn(t,function(n){return{key:n,value:e+"-"+n}}))},Qc=function(e){return sr("partUids","partUids",xt(function(n){return Jc(n.uid,e)}),wr())},Zc=Ec("alloy-premade"),nf=function(n){return Ct(Zc,n)},ef=function(r){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(undefined,[n.getApis()].concat([n].concat(e)))},e=r.toString(),t=e.indexOf(")")+1,o=e.indexOf("("),i=e.substring(o+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:no(i.slice(1))}},n;var n,e,t,o,i},tf=h("alloy-id-"),rf=h("data-alloy-id"),of=tf(),uf=rf(),af=function(n,e){Object.defineProperty(n.dom(),uf,{value:e,writable:!0})},cf=function(n){var e=Te(n)?n.dom()[uf]:null;return E.from(e)},ff=function(n){return Ec(n)},sf=function(n,e,t,r,o){var i,u,a=(u=o,(0<(i=r).length?[kr("parts",i)]:[]).concat([Or("uid"),Mr("dom",{}),Mr("components",[]),mi("originalSpec"),Mr("debug.sketcher",{})]).concat(u));return br(n+" [SpecSchema]",ir(a.concat(e)),t)},lf=function(n,e,t,r,o){var i=df(o),u=Fn(t,function(n){return n.fold(E.none,E.some,E.none,E.none).map(function(n){return kr(n.name,n.schema.concat([mi(Gc())]))}).toArray()}),a=Qc(t),c=sf(n,e,i,u,[a]),f=qc(0,c,t);return r(c,Xc(n,c,f.internals()),i,f.externals())},df=function(n){return n.hasOwnProperty("uid")?n:y({},n,{uid:ff("uid")})},mf=ir([Or("name"),Or("factory"),Or("configFields"),Mr("apis",{}),Mr("extraApis",{})]),gf=ir([Or("name"),Or("factory"),Or("configFields"),Or("partFields"),Mr("apis",{}),Mr("extraApis",{})]),pf=function(n){var i=br("Sketcher for "+n.name,mf,n),e=A(i.apis,ef),t=A(i.extraApis,function(n,e){return eo(n,e)});return y({name:h(i.name),partFields:h([]),configFields:h(i.configFields),sketch:function(n){return e=i.name,t=i.configFields,r=i.factory,o=df(n),r(sf(e,t,o,[],[]),o);var e,t,r,o}},e,t)},vf=function(n){var e=br("Sketcher for "+n.name,gf,n),t=Wc(e.name,e.partFields),r=A(e.apis,ef),o=A(e.extraApis,function(n,e){return eo(n,e)});return y({name:h(e.name),partFields:h(e.partFields),configFields:h(e.configFields),sketch:function(n){return lf(e.name,e.configFields,e.partFields,e.factory,n)},parts:h(t)},r,o)},hf=pf({name:"Button",factory:function(n){var e,t,r,o=(e=n.action,t=function(n,e){e.stop(),ge(n)},r=Yn.detect().deviceType.isTouch()?[Wr(re(),t)]:[Wr(W(),t),Wr(H(),function(n,e){e.cut()})],Gr(Rn([e.map(function(t){return Wr(ee(),function(n,e){t(n),e.stop()})}).toArray(),r]))),i=n.dom.tag,u=function(e){return Et(n.dom,"attributes").bind(function(n){return Et(n,e)})};return{uid:n.uid,dom:n.dom,components:n.components,events:o,behaviours:vc(n.buttonBehaviours,[zi.config({}),lc.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==i)return{role:u("role").getOr("button")};var n=u("type").getOr("button"),e=u("role").map(function(n){return{role:n}}).getOr({});return y({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[Mr("uid",undefined),Or("dom"),Mr("components",[]),pc("buttonBehaviours",[zi,lc]),Er("action"),Er("role"),Mr("eventOrder",{})]}),bf=mo({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return Gr([$r(q(),h(!0))])},exhibit:function(n,e){return to({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),yf=function(n){var e,t,r,o=be.fromHtml(n),i=We(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],Dn(t,function(n,e){var t;return"class"===e.name?n:y({},n,((t={})[e.name]=e.value,t))},{})),a=(r=o,Array.prototype.slice.call(r.dom().classList,0)),c=0===i.length?{}:{innerHtml:Go(o)};return y({tag:Se(o),classes:a,attributes:u},c)},xf=function(n){var e,o,t=(e=n,o={prefix:Fi.prefix()},e.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"===t?r.toString():n}));return yf(t)},wf=function(n){return{dom:xf(n)}},Sf=function(n){return so([Mi.config({toggleClass:Fi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Ai(n,function(n,e){(e?Mi.on:Mi.off)(n)})])},Of=function(n,e){var t=e.ui.registry.getAll().icons;return E.from(t[n]).fold(function(){return xf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return xf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})},Tf=function(n,e,t,r){return hf.sketch({dom:Of(n,r),action:e,buttonBehaviours:mt(so([bf.config({})]),t)})},kf={forToolbar:Tf,forToolbarCommand:function(n,e){return Tf(e,function(){n.execCommand(e)},{},n)},forToolbarStateAction:function(n,e,t,r){var o=Sf(t);return Tf(e,r,o,n)},forToolbarStateCommand:function(n,e){var t=Sf(e);return Tf(e,function(){n.execCommand(e)},t,n)},getToolbarIconButton:Of},Ef=Yn.detect().deviceType.isTouch(),Cf=Pc({schema:[Or("dom")],name:"label"}),Df=function(n){return Pc({name:n+"-edge",overrides:function(r){return r.model.manager.edgeActions[n].fold(function(){return{}},function(t){var n=Gr([_r(F(),t,[r])]),e=Gr([_r(H(),t,[r]),_r(j(),function(n,e){e.mouseIsDown.get()&&t(n,e)},[r])]);return{events:Ef?n:e}})}})},Mf=Df("top-left"),Af=Df("top"),If=Df("top-right"),Bf=Df("right"),Rf=Df("bottom-right"),Ff=Df("bottom"),Vf=Df("bottom-left"),Nf=[Cf,Df("left"),Bf,Af,Ff,Mf,If,Vf,Rf,Lc({name:"thumb",defaults:h({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Gr([Xr(F(),n,"spectrum"),Xr(V(),n,"spectrum"),Xr(N(),n,"spectrum"),Xr(H(),n,"spectrum"),Xr(j(),n,"spectrum"),Xr(z(),n,"spectrum")])}}}),Lc({schema:[Ir("mouseIsDown",function(){return Fo(!1)})],name:"spectrum",overrides:function(t){var r=t.model.manager,o=function(e,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(e,t,n)})},n=Gr([Wr(F(),o),Wr(V(),o)]),e=Gr([Wr(H(),o),Wr(j(),function(n,e){t.mouseIsDown.get()&&o(n,e)})]);return{behaviours:so(Ef?[]:[lc.config({mode:"special",onLeft:function(n){return r.onLeft(n,t)},onRight:function(n){return r.onRight(n,t)},onUp:function(n){return r.onUp(n,t)},onDown:function(n){return r.onDown(n,t)}}),zi.config({})]),events:Ef?n:e}}})],Hf=function(n,e,t){e.store.manager.onLoad(n,e,t)},jf=function(n,e,t){e.store.manager.onUnload(n,e,t)},zf=/* */Object.freeze({onLoad:Hf,onUnload:jf,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),Lf=/* */Object.freeze({events:function(t,r){var n=t.resetOnDom?[Kr(function(n,e){Hf(n,t,r)}),Jr(function(n,e){jf(n,t,r)})]:[ro(t,r,Hf)];return Gr(n)}}),Pf=function(){var n=Fo(null);return fo({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},Uf=function(){var i=Fo({}),u=Fo({});return fo({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Et(i.get(),n).orThunk(function(){return Et(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};kn(n,function(e){r[e.value]=e,Et(e,"meta").each(function(n){Et(n,"text").each(function(n){o[n]=e})})}),i.set(y({},e,r)),u.set(y({},t,o))},clear:function(){i.set({}),u.set({})}})},Gf=/* */Object.freeze({memory:Pf,dataset:Uf,manual:function(){return fo({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),$f=function(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)},Wf=[Er("initialValue"),Or("getFallbackEntry"),Or("getDataKey"),Or("setValue"),di("manager",{setValue:$f,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){$f(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:Uf})],_f=[Or("getValue"),Mr("setValue",x),Er("initialValue"),di("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:x,state:co.init})],qf=[Er("initialValue"),di("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:Pf})],Xf=[Ar("store",{mode:"memory"},xr("mode",{memory:qf,manual:_f,dataset:Wf})),ci("onSetValue"),Mr("resetOnDom",!1)],Yf=mo({fields:Xf,name:"representing",active:Lf,apis:zf,extra:{setValueFrom:function(n,e){var t=Yf.getValue(e);Yf.setValue(n,t)}},state:Gf}),Kf=function(t,r){return{left:h(t),top:h(r),translate:function(n,e){return Kf(t+n,r+e)}}},Jf=Kf,Qf=Yn.detect().deviceType.isTouch(),Zf=h("slider.change.value"),ns=function(n){var e=n.event().raw();if(Qf){var t=e;return t.touches!==undefined&&1===t.touches.length?E.some(t.touches[0]).map(function(n){return Jf(n.clientX,n.clientY)}):E.none()}var r=e;return r.clientX!==undefined?E.some(r).map(function(n){return Jf(n.clientX,n.clientY)}):E.none()},es=function(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)},ts=function(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)},rs=function(n,e,t){return Math.max(e,Math.min(t,n))},os=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var v,h,b,y,x,w,S,O=(x=o,w=l,S=d,Math.min(S,Math.max(x,w))-w),T=rs(O/m*r+e,g,p);return u&&e<=T&&T<=t?(v=T,h=e,b=t,y=i,a.fold(function(){var n=v-h,e=Math.round(n/y)*y;return rs(h+e,h-1,b+1)},function(n){var e=(v-n)%y,t=Math.round(e/y),r=Math.floor((v-n)/y),o=Math.floor((b-n)/y),i=n+Math.min(o,r+t)*y;return Math.max(n,i)})):c?Math.round(T):T},is=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?a:s:(o-e)/r*c},us=Yi("width",function(n){return n.dom().offsetWidth}),as=function(n,e){us.set(n,e)},cs=function(n){return us.get(n)},fs=function(n){return n.model.minX},ss=function(n){return n.model.minY},ls=function(n){return n.model.minX-1},ds=function(n){return n.model.minY-1},ms=function(n){return n.model.maxX},gs=function(n){return n.model.maxY},ps=function(n){return n.model.maxX+1},vs=function(n){return n.model.maxY+1},hs=function(n,e,t){return e(n)-t(n)},bs=function(n){return hs(n,ms,fs)},ys=function(n){return hs(n,gs,ss)},xs=function(n){return bs(n)/2},ws=function(n){return ys(n)/2},Ss=function(n){return n.stepSize},Os=function(n){return n.snapToGrid},Ts=function(n){return n.snapStart},ks=function(n){return n.rounded},Es=function(n,e){return n[e+"-edge"]!==undefined},Cs=function(n){return Es(n,"left")},Ds=function(n){return Es(n,"right")},Ms=function(n){return Es(n,"top")},As=function(n){return Es(n,"bottom")},Is=function(n){return n.model.value.get()},Bs=function(n){return{x:h(n)}},Rs=function(n){return{y:h(n)}},Fs=function(n,e){return{x:h(n),y:h(e)}},Vs=function(n,e){me(n,Zf(),{value:e})},Ns="left",Hs=function(n){return n.element().dom().getBoundingClientRect()},js=function(n,e){return n[e]},zs=function(n){var e=Hs(n);return js(e,Ns)},Ls=function(n){var e=Hs(n);return js(e,"right")},Ps=function(n){var e=Hs(n);return js(e,"top")},Us=function(n){var e=Hs(n);return js(e,"bottom")},Gs=function(n){var e=Hs(n);return js(e,"width")},$s=function(n){var e=Hs(n);return js(e,"height")},Ws=function(n,e,t){return(n+e)/2-t},_s=function(n,e){var t=Hs(n),r=Hs(e),o=js(t,Ns),i=js(t,"right"),u=js(r,Ns);return Ws(o,i,u)},qs=function(n,e){var t=Hs(n),r=Hs(e),o=js(t,"top"),i=js(t,"bottom"),u=js(r,"top");return Ws(o,i,u)},Xs=function(n,e){me(n,Zf(),{value:e})},Ys=function(n){return{x:h(n)}},Ks=function(n,e,t){var r={min:fs(e),max:ms(e),range:bs(e),value:t,step:Ss(e),snap:Os(e),snapStart:Ts(e),rounded:ks(e),hasMinEdge:Cs(e),hasMaxEdge:Ds(e),minBound:zs(n),maxBound:Ls(n),screenRange:Gs(n)};return os(r)},Js=function(u){return function(n,e){return(t=u,r=n,o=e,i=(0<t?ts:es)(Is(o).x(),fs(o),ms(o),Ss(o)),Xs(r,Ys(i)),E.some(i)).map(function(){return!0});var t,r,o,i}},Qs=function(n,e,t,r,o,i){var u,a,c,f,s,l,d,m,g,p=(a=i,c=t,f=r,s=o,l=Gs(u=e),d=f.bind(function(n){return E.some(_s(n,u))}).getOr(0),m=s.bind(function(n){return E.some(_s(n,u))}).getOr(l),g={min:fs(a),max:ms(a),range:bs(a),value:c,hasMinEdge:Cs(a),hasMaxEdge:Ds(a),minBound:zs(u),minOffset:0,maxBound:Ls(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},is(g));return zs(e)-zs(n)+p},Zs=Js(-1),nl=Js(1),el=E.none,tl=E.none,rl={"top-left":E.none(),top:E.none(),"top-right":E.none(),right:E.some(function(n,e){Vs(n,Bs(ps(e)))}),"bottom-right":E.none(),bottom:E.none(),"bottom-left":E.none(),left:E.some(function(n,e){Vs(n,Bs(ls(e)))})},ol=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Ks(n,e,t),o=Ys(r);return Xs(n,o),r},setToMin:function(n,e){var t=fs(e);Xs(n,Ys(t))},setToMax:function(n,e){var t=ms(e);Xs(n,Ys(t))},findValueOfOffset:Ks,getValueFromEvent:function(n){return ns(n).map(function(n){return n.left()})},findPositionOfValue:Qs,setPositionFromValue:function(n,e,t,r){var o=Is(t),i=Qs(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=cs(e.element())/2;Ui(e.element(),"left",i-u+"px")},onLeft:Zs,onRight:nl,onUp:el,onDown:tl,edgeActions:rl}),il=function(n,e){me(n,Zf(),{value:e})},ul=function(n){return{y:h(n)}},al=function(n,e,t){var r={min:ss(e),max:gs(e),range:ys(e),value:t,step:Ss(e),snap:Os(e),snapStart:Ts(e),rounded:ks(e),hasMinEdge:Ms(e),hasMaxEdge:As(e),minBound:Ps(n),maxBound:Us(n),screenRange:$s(n)};return os(r)},cl=function(u){return function(n,e){return(t=u,r=n,o=e,i=(0<t?ts:es)(Is(o).y(),ss(o),gs(o),Ss(o)),il(r,ul(i)),E.some(i)).map(function(){return!0});var t,r,o,i}},fl=function(n,e,t,r,o,i){var u,a,c,f,s,l,d,m,g,p=(a=i,c=t,f=r,s=o,l=$s(u=e),d=f.bind(function(n){return E.some(qs(n,u))}).getOr(0),m=s.bind(function(n){return E.some(qs(n,u))}).getOr(l),g={min:ss(a),max:gs(a),range:ys(a),value:c,hasMinEdge:Ms(a),hasMaxEdge:As(a),minBound:Ps(u),minOffset:0,maxBound:Us(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},is(g));return Ps(e)-Ps(n)+p},sl=E.none,ll=E.none,dl=cl(-1),ml=cl(1),gl={"top-left":E.none(),top:E.some(function(n,e){Vs(n,Rs(ds(e)))}),"top-right":E.none(),right:E.none(),"bottom-right":E.none(),bottom:E.some(function(n,e){Vs(n,Rs(vs(e)))}),"bottom-left":E.none(),left:E.none()},pl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=al(n,e,t),o=ul(r);return il(n,o),r},setToMin:function(n,e){var t=ss(e);il(n,ul(t))},setToMax:function(n,e){var t=gs(e);il(n,ul(t))},findValueOfOffset:al,getValueFromEvent:function(n){return ns(n).map(function(n){return n.top()})},findPositionOfValue:fl,setPositionFromValue:function(n,e,t,r){var o=Is(t),i=fl(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),u=Zi(e.element())/2;Ui(e.element(),"top",i-u+"px")},onLeft:sl,onRight:ll,onUp:dl,onDown:ml,edgeActions:gl}),vl=function(n,e){me(n,Zf(),{value:e})},hl=function(n,e){return{x:h(n),y:h(e)}},bl=function(f,s){return function(n,e){return(t=f,r=s,o=n,i=e,u=0<t?ts:es,a=r?Is(i).x():u(Is(i).x(),fs(i),ms(i),Ss(i)),c=r?u(Is(i).y(),ss(i),gs(i),Ss(i)):Is(i).y(),vl(o,hl(a,c)),E.some(a)).map(function(){return!0});var t,r,o,i,u,a,c}},yl=bl(-1,!1),xl=bl(1,!1),wl=bl(-1,!0),Sl=bl(1,!0),Ol={"top-left":E.some(function(n,e){Vs(n,Fs(ls(e),ds(e)))}),top:E.some(function(n,e){Vs(n,Fs(xs(e),ds(e)))}),"top-right":E.some(function(n,e){Vs(n,Fs(ps(e),ds(e)))}),right:E.some(function(n,e){Vs(n,Fs(ps(e),ws(e)))}),"bottom-right":E.some(function(n,e){Vs(n,Fs(ps(e),vs(e)))}),bottom:E.some(function(n,e){Vs(n,Fs(xs(e),vs(e)))}),"bottom-left":E.some(function(n,e){Vs(n,Fs(ls(e),vs(e)))}),left:E.some(function(n,e){Vs(n,Fs(ls(e),ws(e)))})},Tl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Ks(n,e,t.left()),o=al(n,e,t.top()),i=hl(r,o);return vl(n,i),i},setToMin:function(n,e){var t=fs(e),r=ss(e);vl(n,hl(t,r))},setToMax:function(n,e){var t=ms(e),r=gs(e);vl(n,hl(t,r))},getValueFromEvent:function(n){return ns(n)},setPositionFromValue:function(n,e,t,r){var o=Is(t),i=Qs(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=fl(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),a=cs(e.element())/2,c=Zi(e.element())/2;Ui(e.element(),"left",i-a+"px"),Ui(e.element(),"top",u-c+"px")},onLeft:yl,onRight:xl,onUp:wl,onDown:Sl,edgeActions:Ol}),kl=Yn.detect().deviceType.isTouch(),El=[Mr("stepSize",1),Mr("onChange",x),Mr("onChoose",x),Mr("onInit",x),Mr("onDragStart",x),Mr("onDragEnd",x),Mr("snapToGrid",!1),Mr("rounded",!0),Er("snapStart"),Tr("model",xr("mode",{x:[Mr("minX",0),Mr("maxX",100),Ir("value",function(n){return Fo(n.mode.minX)}),Or("getInitialValue"),di("manager",ol)],y:[Mr("minY",0),Mr("maxY",100),Ir("value",function(n){return Fo(n.mode.minY)}),Or("getInitialValue"),di("manager",pl)],xy:[Mr("minX",0),Mr("maxX",100),Mr("minY",0),Mr("maxY",100),Ir("value",function(n){return Fo({x:h(n.mode.minX),y:h(n.mode.minY)})}),Or("getInitialValue"),di("manager",Tl)]})),dc("sliderBehaviours",[lc,Yf])].concat(kl?[]:[Ir("mouseIsDown",function(){return Fo(!1)})]),Cl=Yn.detect().deviceType.isTouch(),Dl=vf({name:"Slider",configFields:El,partFields:Nf,factory:function(i,n,e,t){var u=function(n){return Kc(n,i,"thumb")},a=function(n){return Kc(n,i,"spectrum")},r=function(n){return Yc(n,i,"left-edge")},o=function(n){return Yc(n,i,"right-edge")},c=function(n){return Yc(n,i,"top-edge")},f=function(n){return Yc(n,i,"bottom-edge")},s=i.model,l=s.manager,d=function(n,e){l.setPositionFromValue(n,e,i,{getLeftEdge:r,getRightEdge:o,getTopEdge:c,getBottomEdge:f,getSpectrum:a})},m=function(n,e){s.value.set(e);var t=u(n);return d(n,t),i.onChange(n,t,e),E.some(!0)},g=[Wr(F(),function(n,e){i.onDragStart(n,u(n))}),Wr(N(),function(n,e){i.onDragEnd(n,u(n))})],p=[Wr(H(),function(n,e){e.stop(),i.onDragStart(n,u(n)),i.mouseIsDown.set(!0)}),Wr(z(),function(n,e){i.onDragEnd(n,u(n))})],v=Cl?g:p;return{uid:i.uid,dom:i.dom,components:n,behaviours:gc(i.sliderBehaviours,Rn([Cl?[]:[lc.config({mode:"special",focusIn:function(n){return Yc(n,i,"spectrum").map(lc.focusIn).map(h(!0))}})],[Yf.config({store:{mode:"manual",getValue:function(n){return s.value.get()}}}),vi.config({channels:{"mouse.released":{onReceive:function(t,n){var e=i.mouseIsDown.get();i.mouseIsDown.set(!1),e&&Yc(t,i,"thumb").each(function(n){var e=s.value.get();i.onChoose(t,n,e)})}}}})]])),events:Gr([Wr(Zf(),function(n,e){m(n,e.event().value())}),Kr(function(n,e){var t=s.getInitialValue();s.value.set(t);var r=u(n);d(n,r);var o=a(n);i.onInit(n,r,o,s.value.get())})].concat(v)),apis:{resetToMin:function(n){l.setToMin(n,i)},resetToMax:function(n){l.setToMax(n,i)},changeValue:m,refresh:d},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Ml=function(e,t,r,n){return kf.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)},Al=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Dl.sketch({dom:xf('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Dl.parts()["left-edge"](wf('<div class="${prefix}-hue-slider-black"></div>')),Dl.parts().spectrum({dom:xf('<div class="${prefix}-slider-gradient-container"></div>'),components:[wf('<div class="${prefix}-slider-gradient"></div>')],behaviours:so([Mi.config({toggleClass:Fi.resolve("thumb-active")})])}),Dl.parts()["right-edge"](wf('<div class="${prefix}-hue-slider-white"></div>')),Dl.parts().thumb({dom:xf('<div class="${prefix}-slider-thumb"></div>'),behaviours:so([Mi.config({toggleClass:Fi.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Ui(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){Mi.on(e)},onDragEnd:function(n,e){Mi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Ui(e.element(),"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},sliderBehaviours:so([Ii(Dl.refresh)])}))];var o,i},Il=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return Ml(n,"color-levels",function(){return Al(e)},r)},Bl=ir([Or("getInitialValue"),Or("onChange"),Or("category"),Or("sizes")]),Rl=function(n){var i=br("SizeSlider",Bl,n);return Dl.sketch({dom:{tag:"div",classes:[Fi.resolve("slider-"+i.category+"-size-container"),Fi.resolve("slider"),Fi.resolve("slider-size-container")]},onChange:function(n,e,t){var r,o=t.x();0<=(r=o)&&r<i.sizes.length&&i.onChange(o)},onDragStart:function(n,e){Mi.on(e)},onDragEnd:function(n,e){Mi.off(e)},model:{mode:"x",minX:0,maxX:i.sizes.length-1,getInitialValue:function(){return{x:function(){return i.getInitialValue()}}}},stepSize:1,snapToGrid:!0,sliderBehaviours:so([Ii(Dl.refresh)]),components:[Dl.parts().spectrum({dom:xf('<div class="${prefix}-slider-size-container"></div>'),components:[wf('<div class="${prefix}-slider-size-line"></div>')]}),Dl.parts().thumb({dom:xf('<div class="${prefix}-slider-thumb"></div>'),behaviours:so([Mi.config({toggleClass:Fi.resolve("thumb-active")})])})]})},Fl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Vl=function(n){var e,t,r=n.selection.getStart(),o=be.fromDom(r),i=be.fromDom(n.getBody()),u=(e=function(n){return Ue(i,n)},(Te(t=o)?E.some(t):$e(t)).map(function(n){return Lr(n,function(n){return _i(n,"font-size").isSome()},e).bind(function(n){return _i(n,"font-size")}).getOrThunk(function(){return $i(n,"font-size")})}).getOr(""));return Mn(Fl,function(n){return u===n}).getOr("medium")},Nl={candidates:h(Fl),get:function(n){var e,t=Vl(n);return(e=t,An(Fl,function(n){return n===e})).getOr(2)},apply:function(r,n){var e;(e=n,E.from(Fl[e])).each(function(n){var e,t;t=n,Vl(e=r)!==t&&e.execCommand("fontSize",!1,t)})}},Hl=Nl.candidates(),jl=function(n){return[wf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,Rl({onChange:e.onChange,sizes:Hl,category:"font",getInitialValue:e.getInitialValue})),wf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},zl=function(n){var e=function t(n){return n.uid!==undefined}(n)&&At(n,"uid")?n.uid:ff("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(E.none,E.some)},asSpec:function(){return y({},n,{uid:e})}}};function Ll(n,e){return Ul(v.document.createElement("canvas"),n,e)}function Pl(n){return n.getContext("2d")}function Ul(n,e,t){return n.width=e,n.height=t,n}var Gl={create:Ll,clone:function Vh(n){var e;return Pl(e=Ll(n.width,n.height)).drawImage(n,0,0),e},resize:Ul,get2dContext:Pl,get3dContext:function Nh(n){var e=null;try{e=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(t){}return e||(e=null),e}},$l={getWidth:function Hh(n){return n.naturalWidth||n.width},getHeight:function jh(n){return n.naturalHeight||n.height}},Wl=window.Promise?window.Promise:function(){var n=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],f(n,r(o,this),r(u,this))},e=n.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)};function r(n,e){return function(){n.apply(e,arguments)}}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function i(r){var o=this;null!==this._state?e(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void f(r(e,n),r(o,this),r(u,this))}this._state=!0,this._value=n,a.call(this)}catch(t){u.call(this,t)}}function u(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds.length;n<e;n++)i.call(this,this._deferreds[n]);this._deferreds=null}function c(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function f(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}return n.prototype["catch"]=function(n){return this.then(null,n)},n.prototype.then=function(t,r){var o=this;return new n(function(n,e){i.call(o,new c(t,r,n,e))})},n.all=function(){var c=Array.prototype.slice.call(1===arguments.length&&t(arguments[0])?arguments[0]:arguments);return new n(function(o,i){if(0===c.length)return o([]);var u=c.length;function a(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){a(e,n)},i)}c[e]=n,0==--u&&o(c)}catch(r){i(r)}}for(var n=0;n<c.length;n++)a(n,c[n])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(n){n(e)})},n.reject=function(t){return new n(function(n,e){e(t)})},n.race=function(o){return new n(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},n}();function _l(){return new(Ve.getOrDie("FileReader"))}var ql={atob:function(n){return Ve.getOrDie("atob")(n)},requestAnimationFrame:function(n){Ve.getOrDie("requestAnimationFrame")(n)}};function Xl(a){return new Wl(function(n,e){var t=v.URL.createObjectURL(a),r=new v.Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)};function i(){o(),n(r)}function u(){o(),e("Unable to load data of type "+a.type+": "+t)}r.addEventListener("load",i),r.addEventListener("error",u),r.src=t,r.complete&&i()})}function Yl(r){return new Wl(function(n,t){var e=new v.XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200==this.status&&n(this.response)},e.onerror=function(){var n,e=this;t(0===this.status?((n=new Error("No access to download image")).code=18,n.name="SecurityError",n):new Error("Error "+e.status+" downloading image"))},e.send()})}function Kl(n){var e=n.split(","),t=/data:([^;]+)/.exec(e[0]);if(!t)return E.none();for(var r,o=t[1],i=e[1],u=ql.atob(i),a=u.length,c=Math.ceil(a/1024),f=new Array(c),s=0;s<c;++s){for(var l=1024*s,d=Math.min(l+1024,a),m=new Array(d-l),g=l,p=0;g<d;++p,++g)m[p]=u[g].charCodeAt(0);f[s]=(r=m,new(Ve.getOrDie("Uint8Array"))(r))}return E.some(function v(n,e){return new(Ve.getOrDie("Blob"))(n,e)}(f,{type:o}))}function Jl(t){return new Wl(function(n,e){Kl(t).fold(function(){e("uri is not base64: "+t)},n)})}function Ql(t){return new Wl(function(n){var e=_l();e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}var Zl,nd,ed,td={blobToImage:Xl,imageToBlob:function zh(n){var e=n.src;return 0===e.indexOf("data:")?Jl(e):Yl(e)},blobToArrayBuffer:function Lh(t){return new Wl(function(n){var e=_l();e.onloadend=function(){n(e.result)},e.readAsArrayBuffer(t)})},blobToDataUri:Ql,blobToBase64:function Ph(n){return Ql(n).then(function(n){return n.split(",")[1]})},dataUriToBlobSync:Kl,canvasToBlob:function Uh(n,t,r){return t=t||"image/png",v.HTMLCanvasElement.prototype.toBlob?new Wl(function(e){n.toBlob(function(n){e(n)},t,r)}):Jl(n.toDataURL(t,r))},canvasToDataURL:function Gh(n,e,t){return e=e||"image/png",n.then(function(n){return n.toDataURL(e,t)})},blobToCanvas:function $h(n){return Xl(n).then(function(n){var e;return function t(n){v.URL.revokeObjectURL(n.src)}(n),e=Gl.create($l.getWidth(n),$l.getHeight(n)),Gl.get2dContext(e).drawImage(n,0,0),e})},uriToBlob:function Wh(n){return 0===n.indexOf("blob:")?Yl(n):0===n.indexOf("data:")?Jl(n):null}},rd=function(n){return td.blobToBase64(n)},od=function(u){var e=zl({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:Gr([Yr(W()),Wr($(),function(n,e){var t,r,o;(t=e,r=t.event(),o=r.raw().target.files||r.raw().dataTransfer.files,E.from(o[0])).each(function(n){var o,i;o=u,rd(i=n).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Ec("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})})})])});return hf.sketch({dom:kf.getToolbarIconButton("image",u),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},id=function(n){return n.dom().textContent},ud=function(n){return 0<n.length},ad=function(n){return n===undefined||null===n?"":n},cd=function(e,t,n){return n.text.toOption().filter(ud).fold(function(){return yo(n=e,"href")===id(n)?E.some(t):E.none();var n},E.some)},fd=function(n){var e=be.fromDom(n.selection.getStart());return uu(e,"a")},sd={getInfo:function(n){return fd(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:E.none()}},function(n){return t=id(e=n),r=yo(e,"href"),o=yo(e,"title"),i=yo(e,"target"),{url:ad(r),text:t!==r?ad(t):"",title:ad(o),target:ad(i),link:E.some(e)};var e,t,r,o,i})},applyInfo:function(o,i){i.url.toOption().filter(ud).fold(function(){var e;e=o,i.link.bind(b).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.toOption().filter(ud).each(function(n){t.title=n}),n.target.toOption().filter(ud).each(function(n){t.target=n}),t);i.link.bind(b).fold(function(){var n=i.text.toOption().filter(ud).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=cd(t,e,i);bo(t,r),n.each(function(n){var e;e=n,t.dom().textContent=e})})})},query:fd},ld=Yn.detect(),dd=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},md=function(n,e){(ld.os.isAndroid()?dd:s)(e,n)},gd=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=Gr(e),mo({fields:[Or("enabled")],name:t,active:{events:h(r)}})),configAsRaw:h({}),initialConfig:{},state:co}}},pd=/* */Object.freeze({getCurrent:function(n,e,t){return e.find(n)}}),vd=[Or("find")],hd=mo({fields:vd,name:"composing",apis:pd}),bd=pf({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=c(e,["attributes"]);return{uid:n.uid,dom:y({tag:"div",attributes:y({role:"presentation"},t)},r),components:n.components,behaviours:mc(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[Mr("components",[]),dc("containerBehaviours",[]),Mr("events",{}),Mr("domModification",{}),Mr("eventOrder",{})]}),yd=pf({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:vc(t.dataBehaviours,[Yf.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),hd.config({find:E.some})]),events:Gr([Kr(function(n,e){Yf.setValue(n,t.getInitialValue())})])}},configFields:[Or("uid"),Or("dom"),Or("getInitialValue"),pc("dataBehaviours",[Yf,hd])]}),xd=function(n){return n.dom().value},wd=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},Sd=h([Er("data"),Mr("inputAttributes",{}),Mr("inputStyles",{}),Mr("tag","input"),Mr("inputClasses",[]),ci("onSetValue"),Mr("styles",{}),Mr("eventOrder",{}),dc("inputBehaviours",[Yf,zi]),Mr("selectOnFocus",!0)]),Od=function(n){return y({},(e=n,so([zi.config({onFocus:!1===e.selectOnFocus?x:function(n){var e=n.element(),t=xd(e);e.dom().setSelectionRange(0,t.length)}})])),gc(n.inputBehaviours,[Yf.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return xd(n.element())},setValue:function(n,e){xd(n.element())!==e&&wd(n.element(),e)}},onSetValue:n.onSetValue})]));var e},Td=pf({name:"Input",configFields:Sd(),factory:function(n,e){return{uid:n.uid,dom:(t=n,{tag:t.tag,attributes:y({type:"input"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}),components:[],behaviours:Od(n),eventOrder:n.eventOrder};var t}}),kd=/* */Object.freeze({exhibit:function(n,e){return to({attributes:Dt([{key:e.tabAttr,value:"true"}])})}}),Ed=[Mr("tabAttr","data-alloy-tabstop")],Cd=mo({fields:Ed,name:"tabstopping",active:kd}),Dd=function(n,e){var t=zl(Td.sketch({inputAttributes:{placeholder:e},onSetValue:function(n,e){de(n,G())},inputBehaviours:so([hd.config({find:E.some}),Cd.config({}),lc.config({mode:"execution"})]),selectOnFocus:!1})),r=zl(hf.sketch({dom:xf('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);Yf.setValue(e,"")}}));return{name:n,spec:bd.sketch({dom:xf('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:so([Mi.config({toggleClass:Fi.resolve("input-container-empty")}),hd.config({find:function(n){return E.some(t.get(n))}}),gd("input-clearing",[Wr(G(),function(n){var e=t.get(n);(0<Yf.getValue(e).length?Mi.off:Mi.on)(n)})])])})}},Md=["input","button","textarea"],Ad=function(n,e,t){e.disabled&&Nd(n,e,t)},Id=function(n){return Sn(Md,Se(n.element()))},Bd=function(n){ho(n.element(),"disabled","disabled")},Rd=function(n){wo(n.element(),"disabled")},Fd=function(n){ho(n.element(),"aria-disabled","true")},Vd=function(n){ho(n.element(),"aria-disabled","false")},Nd=function(e,n,t){n.disableClass.each(function(n){Eo(e.element(),n)}),(Id(e)?Bd:Fd)(e)},Hd=function(n){return Id(n)?xo(n.element(),"disabled"):"true"===yo(n.element(),"aria-disabled")},jd=/* */Object.freeze({enable:function(e,n,t){n.disableClass.each(function(n){Do(e.element(),n)}),(Id(e)?Rd:Vd)(e)},disable:Nd,isDisabled:Hd,onLoad:Ad}),zd=/* */Object.freeze({exhibit:function(n,e,t){return to({classes:e.disabled?e.disableClass.map(jn).getOr([]):[]})},events:function(n,e){return Gr([$r(ee(),function(n,e){return Hd(n)}),ro(n,e,Ad)])}}),Ld=[Mr("disabled",!1),Er("disableClass")],Pd=mo({fields:Ld,name:"disabling",active:zd,apis:jd}),Ud=[dc("formBehaviours",[Yf])],Gd=function(n){return"<alloy.field."+n+">"},$d=function(o,n,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:gc(o.formBehaviours,[Yf.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),A(e.partUids,function(n,e){return h(t.getByUid(n))}));return A(r,function(n,e){return n().bind(function(n){var e,t=hd.getCurrent(n);return e="missing current",t.fold(function(){return ft.error(e)},ft.value)}).map(Yf.getValue)})},setValue:function(t,n){M(n,function(e,n){Yc(t,o,n).each(function(n){hd.getCurrent(n).each(function(n){Yf.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return Yc(n,o,e).bind(hd.getCurrent)}}}},Wd=(ef(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Gd(n),o=e,{uiType:Tc(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=Tn(r,function(n){return Lc({name:n,pname:Gd(n)})});return lf("form",Ud,o,$d,t)}),_d=function(){var e=Fo(E.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(E.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(E.some(n))},run:function(n){e.get().each(n)}}},qd=function(){var e=Fo(E.none());return{clear:function(){e.set(E.none())},set:function(n){e.set(E.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}},Xd=function(n){return{xValue:n,points:[]}},Yd=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},Kd=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},Jd=function(n){var r="navigateEvent",e=ur([Or("fields"),Mr("maxFieldIndex",n.fields.length-1),Or("onExecute"),Or("getInitialValue"),Ir("state",function(){return{dialogSwipeState:qd(),currentScreen:Fo(0)}})]),u=br("SerialisedDialog",e,n),o=function(e,n,t){return hf.sketch({dom:xf('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){me(n,r,{direction:e})},buttonBehaviours:so([Pd.config({disableClass:Fi.resolve("toolbar-navigation-disabled"),disabled:!t})])})},i=function(n,o){var i=tu(n.element(),"."+Fi.resolve("serialised-dialog-screen"));iu(n.element(),"."+Fi.resolve("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(_i(r,"left").each(function(n){var e=parseInt(n,10),t=cs(i[0]);Ui(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},a=function(r){var n=tu(r.element(),"input");E.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=f.get(r);Mu.highlightAt(e,u.state.currentScreen.get())},c=zl(Wd(function(t){return{dom:xf('<div class="${prefix}-serialised-dialog"></div>'),components:[bd.sketch({dom:xf('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:Tn(u.fields,function(n,e){return e<=u.maxFieldIndex?bd.sketch({dom:xf('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[o(-1,"previous",0<e),t.field(n.name,n.spec),o(1,"next",e<u.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:so([Ii(function(n,e){var t;t=e,iu(n.element(),"."+Fi.resolve("serialised-dialog-chain")).each(function(n){Ui(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),lc.config({mode:"special",focusIn:function(n){a(n)},onTab:function(n){return i(n,1),E.some(!0)},onShiftTab:function(n){return i(n,-1),E.some(!0)}}),gd("form-events",[Kr(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=f.get(e);Mu.highlightFirst(t),u.getInitialValue(e).each(function(n){Yf.setValue(e,n)})}),Zr(u.onExecute),Wr(_(),function(n,e){"left"===e.event().raw().propertyName&&a(n)}),Wr(r,function(n,e){var t=e.event().direction();i(n,t)})])])}})),f=zl({dom:xf('<div class="${prefix}-dot-container"></div>'),behaviours:so([Mu.config({highlightClass:Fi.resolve("dot-active"),itemClass:Fi.resolve("dot-item")})]),components:Fn(u.fields,function(n,e){return e<=u.maxFieldIndex?[wf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:xf('<div class="${prefix}-serializer-wrapper"></div>'),components:[c.asSpec(),f.asSpec()],behaviours:so([lc.config({mode:"special",focusIn:function(n){var e=c.get(n);lc.focusIn(e)}}),gd("serializer-wrapper-events",[Wr(F(),function(n,e){var t=e.event();u.state.dialogSwipeState.set(Xd(t.raw().touches[0].clientX))}),Wr(V(),function(n,e){var t=e.event();u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(Yd(n,t.raw().touches[0].clientX))})}),Wr(N(),function(r){u.state.dialogSwipeState.on(function(n){var e=c.get(r),t=-1*Kd(n);i(e,t)})})])])}},Qd=X(function(t,r){return[{label:"the link group",items:[Jd({fields:[Dd("url","Type or paste URL"),Dd("text","Link text"),Dd("title","Link title"),Dd("target","Link target"),(n="link",{name:n,spec:yd.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return E.none()}})})],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return E.some(sd.getInfo(r))},onExecute:function(n){var e=Yf.getValue(n);sd.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}];var n}),Zd=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],nm=Gr([(Zl=Jn(),nd=function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Ue(t=o,n.element())&&!Ue(t,r)&&(v.console.warn(Jn()+" did not get interpreted by the desired target. \nOriginator: "+qo(o)+"\nTarget: "+qo(i)+"\nCheck the "+Jn()+" event handlers"),1))},{key:Zl,value:Rr({can:nd})})]),em=/* */Object.freeze({events:nm}),tm=b,rm=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+qo(e().element())+" is not in context.")}};return{debugInfo:h("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:h(!1)}},om=rm(),im=function(n,o){var i={};return M(n,function(n,r){M(n,function(n,e){var t=kt(e,[])(i);i[e]=t.concat([o(r,n)])})}),i},um=function(n,e){return t=l.apply(undefined,[n.handler].concat(e)),r=n.purpose(),{cHandler:t,purpose:h(r)};var t,r},am=function(n){return n.cHandler},cm=function(n,e){return{name:h(n),handler:h(e)}},fm=function(n,e,t){var r,o,i=y({},t,(r=n,o={},kn(e,function(n){o[n.name()]=n.handlers(r)}),o));return im(i,cm)},sm=function(n){var e,i=yn(e=n)?{can:h(!0),abort:h(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},lm=function(n,e,t){var r,o,i=e[t];return i?function(u,a,n,c){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[a](),r=e[a](),o=c.indexOf(t),i=c.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+Jt(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+Jt(c,null,2));return o<i?-1:i<o?1:0});return ft.value(t)}catch(r){return ft.error([r])}}("Event: "+t,"name",n,i).map(function(n){var e=Tn(n,function(n){return n.handler()});return Fr(e)}):(r=t,o=n,ft.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+Jt(Tn(o,function(n){return n.name()}),null,2)]))},dm=function(n,i){var e=B(n,function(r,o){return(1===r.length?ft.value(r[0].handler()):lm(r,i,o)).map(function(n){var e=sm(n),t=1<r.length?En(i,function(e){return Sn(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return Ct(o,{handler:e,purpose:h(t)})})});return Mt(e,{})},mm=function(n){return vr("custom.definition",ur([sr("dom","dom",ht(),ur([Or("tag"),Mr("styles",{}),Mr("classes",[]),Mr("attributes",{}),Er("value"),Er("innerHtml")])),Or("components"),Or("uid"),Mr("events",{}),Mr("apis",{}),sr("eventOrder","eventOrder",(e={"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]},pt.mergeWithThunk(h(e))),wr()),Er("domModification")]),n);var e},gm=function(e,n){kn(n,function(n){Eo(e,n)})},pm=function(e,n){kn(n,function(n){Do(e,n)})},vm=function(n,e){return t=n,o=Tn(r=e,function(n){return Cr(n.name(),[Or("config"),Mr("state",co)])}),i=vr("component.behaviours",ur(o),t.behaviours).fold(function(n){throw new Error(yr(n)+"\nComplete spec:\n"+Jt(t,null,2))},function(n){return n}),{list:r,data:A(i,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})};var t,r,o,i},hm=function(n){var e,t,r,o=(e=n,t=kt("behaviours",{})(e),r=En(C(t),function(n){return t[n]!==undefined}),Tn(r,function(n){return t[n].me}));return vm(n,o)},bm=function(n,e,t){var r,o,i,u=y({},(r=n).dom,{uid:r.uid,domChildren:Tn(r.components,function(n){return n.element()})}),a=n.domModification.fold(function(){return to({})},to),c={"alloy.base.modification":a},f=0<e.length?function(e,n,t,r){var o=y({},n);kn(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=im(o,function(n,e){return{name:n,modification:e}}),u=function(n){return Cn(n,function(n,e){return y({},e.modification,n)},{})},a=Cn(i.classes,function(n,e){return e.modification.concat(n)},[]),c=u(i.attributes),f=u(i.styles);return to({classes:a,attributes:c,styles:f})}(t,c,e,u):a;return i=f,y({},o=u,{attributes:y({},o.attributes,i.attributes),styles:y({},o.styles,i.styles),classes:o.classes.concat(i.classes)})},ym=function(n,e,t){var r,o,i,u,a,c,f={"alloy.base.behaviour":(r=n,r.events)};return(o=t,i=n.eventOrder,u=e,a=f,c=fm(o,u,a),dm(c,i)).getOrDie()},xm=function(t){var n=function(){return s},r=Fo(om),e=hr(mm(t)),o=hm(t),i=o.list,u=o.data,a=function(n){var e=be.fromTag(n.tag);bo(e,n.attributes),gm(e,n.classes),Gi(e,n.styles),n.innerHtml.each(function(n){return $o(e,n)});var t=n.domChildren;return Je(e,t),n.value.each(function(n){wd(e,n)}),n.uid,af(e,n.uid),e}(bm(e,i,u)),c=ym(e,i,u),f=Fo(e.components),s={getSystem:r.get,config:function(n){var e=u;return(yn(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+Jt(t,null,2))})()},hasConfigured:function(n){return yn(u[n.name()])},spec:h(t),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(rm(n))},element:h(a),syncComponents:function(){var n=We(a),e=Fn(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:h(c)};return s},wm=function(n){var e,t,r=tm(n),o=r.events,i=c(r,["events"]),u=(e=i,t=kt("components",[])(e),Tn(t,km)),a=y({},i,{events:y({},em,o),components:u});return ft.value(xm(a))},Sm=function(n){var e=be.fromText(n);return Om({element:e})},Om=function(n){var e=br("external.component",ir([Or("element"),Er("uid")]),n),t=Fo(rm());e.uid.each(function(n){af(e.element,n)});var r={getSystem:t.get,config:E.none,hasConfigured:h(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(rm(function(){return r}))},getApis:function(){return{}},element:h(e.element),spec:h(n),readState:h("No state"),syncComponents:x,components:h([]),events:h({})};return nf(r)},Tm=ff,km=function(e){return(n=e,Et(n,Zc)).fold(function(){var n=e.hasOwnProperty("uid")?e:y({uid:Tm("")},e);return wm(n).getOrDie()},function(n){return n});var n},Em=nf,Cm="alloy.item-hover",Dm="alloy.item-focus",Mm=function(n){(jo(n.element()).isNone()||zi.isFocused(n))&&(zi.isFocused(n)||zi.focus(n),me(n,Cm,{item:n}))},Am=function(n){me(n,Dm,{item:n})},Im=h(Cm),Bm=h(Dm),Rm=[Or("data"),Or("components"),Or("dom"),Mr("hasSubmenu",!1),Er("toggling"),pc("itemBehaviours",[Mi,zi,lc,Yf]),Mr("ignoreFocus",!1),Mr("domModification",{}),di("builder",function(n){return{dom:n.dom,domModification:y({},n.domModification,{attributes:y({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes,{"aria-haspopup":n.hasSubmenu},n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:vc(n.itemBehaviours,[n.toggling.fold(Mi.revoke,function(n){return Mi.config(y({aria:{mode:"checked"}},n))}),zi.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){Am(n)}}),lc.config({mode:"execution"}),Yf.config({store:{mode:"memory",initialValue:n.data}}),gd("item-type-events",[Wr(oe(),ge),Yr(H()),Wr(L(),Mm),Wr(te(),zi.focus)])]),components:n.components,eventOrder:n.eventOrder}}),Mr("eventOrder",{})],Fm=[Or("dom"),Or("components"),di("builder",function(n){return{dom:n.dom,components:n.components,events:Gr([(e=te(),Wr(e,function(n,e){e.stop()}))])};var e})],Vm=h([Lc({name:"widget",overrides:function(e){return{behaviours:so([Yf.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),Nm=[Or("uid"),Or("data"),Or("components"),Or("dom"),Mr("autofocus",!1),Mr("ignoreFocus",!1),pc("widgetBehaviours",[Yf,zi,lc]),Mr("domModification",{}),Qc(Vm()),di("builder",function(t){var n=qc(0,t,Vm()),e=Xc("item-widget",t,n.internals()),r=function(n){return Yc(n,t,"widget").map(function(n){return lc.focusIn(n),n})},o=function(n,e){return ju(e.event().target())||t.autofocus&&e.setSource(n.element()),E.none()};return{dom:t.dom,components:e,domModification:t.domModification,events:Gr([Zr(function(n,e){r(n).each(function(n){e.stop()})}),Wr(L(),Mm),Wr(te(),function(n,e){t.autofocus?r(n):zi.focus(n)})]),behaviours:vc(t.widgetBehaviours,[Yf.config({store:{mode:"memory",initialValue:t.data}}),zi.config({ignore:t.ignoreFocus,onFocus:function(n){Am(n)}}),lc.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:po(),onLeft:o,onRight:o,onEscape:function(n,e){return zi.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element()),E.none()):(zi.focus(n),E.some(!0))}})])}})],Hm=xr("type",{widget:Nm,item:Rm,separator:Fm}),jm=h([Uc({factory:{sketch:function(n){var e=br("menu.spec item",Hm,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:y({},e,{uid:ff("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),zm=h([Or("value"),Or("items"),Or("dom"),Or("components"),Mr("eventOrder",{}),dc("menuBehaviours",[Mu,Yf,hd,lc]),Ar("movement",{mode:"menu",moveOnTab:!0},xr("mode",{grid:[gi(),di("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[di("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Or("rowSelector")],menu:[Mr("moveOnTab",!0),di("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Tr("markers",ii()),Mr("fakeFocus",!1),Mr("focusManager",Iu()),ci("onHighlight")]),Lm=h("alloy.menu-focus"),Pm=vf({name:"Menu",configFields:zm(),partFields:jm(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:gc(n.menuBehaviours,[Mu.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),Yf.config({store:{mode:"memory",initialValue:n.value}}),hd.config({find:E.some}),lc.config(n.movement.config(n,n.movement))]),events:Gr([Wr(Bm(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){Mu.highlight(e,n),t.stop(),me(e,Lm(),{menu:e,item:n})})}),Wr(Im(),function(n,e){var t=e.event().item();Mu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Um=function(n,e,t,r){var o=n.getSystem().build(r);rt(n,o,t)},Gm=function(n,e,t,r){var o=$m(n,e);Mn(o,function(n){return Ue(r.element(),n.element())}).each(it)},$m=function(n,e){return n.components()},Wm=function(e,t,n,o,r){var i=$m(e,t);return E.from(i[o]).map(function(n){return Gm(e,t,0,n),r.each(function(n){Um(e,0,function(n,e){var t,r;r=e,_e(t=n,o).fold(function(){Ke(t,r)},function(n){qe(n,r)})},n)}),n})},_m=mo({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,e,t,r){Um(n,0,Ke,r)},prepend:function(n,e,t,r){Um(n,0,Ye,r)},remove:Gm,replaceAt:Wm,replaceBy:function(e,t,n,r,o){var i=$m(e,t);return An(i,r).bind(function(n){return Wm(e,t,0,n,o)})},set:function(e,n,t,r){var o,i,u,a,c,f;i=(o=e).components(),kn(i,ot),Qe(o.element()),o.syncComponents(),u=function(){var n=Tn(r,e.getSystem().build);kn(n,function(n){tt(e,n)})},a=e.element(),c=Ge(a),f=Ho(c).bind(function(e){var n=function(n){return Ue(e,n)};return n(a)?E.some(a):Pr(a,n)}),u(a),f.each(function(e){Ho(c).filter(function(n){return Ue(n,e)}).fold(function(){Vo(e)},x)})},contents:$m})}),qm=function(t,r,o,n){return Et(o,n).bind(function(n){return Et(t,n).bind(function(n){var e=qm(t,r,o,n);return E.some([n].concat(e))})}).getOr([])},Xm=function(n,e){var t={};M(n,function(n,e){kn(n,function(n){t[n]=e})});var r=e,o=I(e,function(n,e){return{k:n,v:e}}),i=A(o,function(n,e){return[e].concat(qm(t,r,o,e))});return A(t,function(n){return Et(i,n).getOr([n])})},Ym=function(){var i=Fo({}),u=Fo({}),a=Fo({}),c=Fo(E.none()),f=Fo({}),e=function(n){return Et(u.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(y({},u.get(),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){c.set(E.some(n)),i.set(t),u.set(e),f.set(r);var o=Xm(r,t);a.set(o)},expand:function(t){return Et(i.get(),t).map(function(n){var e=Et(a.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return Et(a.get(),n)},collapse:function(n){return Et(a.get(),n).bind(function(n){return 1<n.length?E.some(n.slice(1)):E.none()})},lookupMenu:e,otherMenus:function(n){var e,t,r=f.get();return e=C(r),t=n,En(e,function(n){return!Sn(t,n)})},getPrimary:function(){return c.get().bind(function(n){return e(n).bind(function(n){return"prepared"===n.type?E.some(n.menu):E.none()})})},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(E.none())},isClear:function(){return c.get().isNone()}}},Km=h("collapse-item"),Jm=pf({name:"TieredMenu",configFields:[li("onExecute"),li("onEscape"),si("onOpenMenu"),si("onOpenSubmenu"),ci("onCollapseMenu"),Mr("highlightImmediately",!0),kr("data",[Or("primary"),Or("menus"),Or("expansions")]),Mr("fakeFocus",!1),ci("onHighlight"),ci("onHover"),kr("markers",[Or("backgroundMenu")].concat(ri()).concat(oi())),Or("dom"),Mr("navigateOnHover",!0),Mr("stayInDom",!1),dc("tmenuBehaviours",[lc,Mu,hd,_m]),Mr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)}},factory:function(a,n){var c,e,i=Fo(E.none()),u=function(r,o,n){return A(n,function(n,e){var t=function(){return Pm.sketch(y({dom:n.dom},n,{value:e,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?(o=function(n){return Mu.getHighlighted(n).map(function(n){return n.element()})},{get:o,set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(x,function(n){Mu.highlight(e,n)});var r=o(e);Au(e,t,r)}}):Iu()}));var o};return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})},f=Ym(),s=function(n){return Yf.getValue(n).value},l=function(n){return A(a.data.menus,function(n,e){return Fn(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},d=function(e,n){Mu.highlight(e,n),Mu.getHighlighted(n).orThunk(function(){return Mu.getFirst(n)}).each(function(n){pe(e,n.element(),te())})},m=function(e,n){return Yo(Tn(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?E.some(n.menu):E.none()})}))},g=function(e,n,t){var r=m(n,n.otherMenus(t));kn(r,function(n){pm(n.element(),[a.markers.backgroundMenu]),a.stayInDom||_m.remove(e,n)})},p=function(n,r){var o,e=(o=n,i.get().getOrThunk(function(){var t={},n=tu(o.element(),"."+a.markers.item),e=En(n,function(n){return"true"===yo(n,"aria-haspopup")});return kn(e,function(n){o.getSystem().getByDom(n).each(function(n){var e=s(n);t[e]=n})}),i.set(E.some(t)),t}));M(e,function(n,e){var t=Sn(r,e);ho(n.element(),"aria-expanded",t)})},v=function(r,o,i){return E.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return E.none();var e=n.menu,t=m(o,i.slice(1));return kn(t,function(n){Eo(n.element(),a.markers.backgroundMenu)}),Ee(e.element())||_m.append(r,Em(e)),pm(e.element(),[a.markers.backgroundMenu]),d(r,e),g(r,o,i),E.some(e)})})};(e=c||(c={}))[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";var h=function(o,i,u){void 0===u&&(u=c.HighlightSubmenu);var n=s(i);return f.expand(n).bind(function(r){return p(o,r),E.from(r[0]).bind(function(t){return f.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return f.setMenuBuilt(e,r),r}(o,t,n);return Ee(e.element())||_m.append(o,Em(e)),a.onOpenSubmenu(o,i,e),u===c.HighlightSubmenu?(Mu.highlightFirst(e),v(o,f,r)):(Mu.dehighlightAll(e),E.some(i))})})})},r=function(e,t){var n=s(t);return f.collapse(n).bind(function(n){return p(e,n),v(e,f,n).map(function(n){return a.onCollapseMenu(e,t,n),n})})},t=function(t){return function(e,n){return uu(n.getSource(),"."+a.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}},o=Gr([Wr(Lm(),function(e,n){var t=n.event().menu();Mu.highlight(e,t);var r=s(n.event().item());f.refresh(r).each(function(n){return g(e,f,n)})}),Zr(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===s(n).indexOf("collapse-item")&&r(e,n),h(e,n,c.HighlightSubmenu).fold(function(){a.onExecute(e,n)},function(){})})}),Kr(function(e,n){var t,r,o;(t=e,r=u(t,a.data.primary,a.data.menus),o=l(t),f.setContents(a.data.primary,r,a.data.expansions,o),f.getPrimary()).each(function(n){_m.append(e,Em(n)),a.onOpenMenu(e,n),a.highlightImmediately&&d(e,n)})})].concat(a.navigateOnHover?[Wr(Im(),function(n,e){var t,r,o=e.event().item();t=n,r=s(o),f.refresh(r).bind(function(n){return p(t,n),v(t,f,n)}),h(n,o,c.HighlightParent),a.onHover(n,o)})]:[])),b={collapseMenu:function(e){Mu.getHighlighted(e).each(function(n){Mu.getHighlighted(n).each(function(n){r(e,n)})})},highlightPrimary:function(e){f.getPrimary().each(function(n){d(e,n)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:gc(a.tmenuBehaviours,[lc.config({mode:"special",onRight:t(function(n,e){return ju(e.element())?E.none():h(n,e,c.HighlightSubmenu)}),onLeft:t(function(n,e){return ju(e.element())?E.none():r(n,e)}),onEscape:t(function(n,e){return r(n,e).orThunk(function(){return a.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){f.getPrimary().each(function(n){pe(e,n.element(),te())})}}),Mu.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),hd.config({find:function(n){return Mu.getHighlighted(n)}}),_m.config({})]),eventOrder:a.eventOrder,apis:b,events:o}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:Ct(n,e),expansions:{}}},collapseItem:function(n){return{value:Ec(Km()),meta:{text:n}}}}}),Qm=function(n,e,t,r){return Et(e.routes,r.start).bind(function(n){return Et(n,r.destination)})},Zm=function(n,e,t,r){return Qm(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},ng=function(t,r,n){var e,o,i;(e=t,o=r,i=n,eg(e,o,i).bind(function(n){return Zm(e,o,i,n)})).each(function(n){var e=n.transition;Do(t.element(),e.transitionClass),wo(t.element(),r.destinationAttr)})},eg=function(n,e,t){var r=n.element();return xo(r,e.destinationAttr)?E.some({start:yo(n.element(),e.stateAttr),destination:yo(n.element(),e.destinationAttr)}):E.none()},tg=function(n,e,t,r){ng(n,e,t),xo(n.element(),e.stateAttr)&&yo(n.element(),e.stateAttr)!==r&&e.onFinish(n,r),ho(n.element(),e.stateAttr,r)},rg=/* */Object.freeze({findRoute:Qm,disableTransition:ng,getCurrentRoute:eg,jumpTo:tg,progressTo:function(t,r,o,i){var n,e;e=r,xo((n=t).element(),e.destinationAttr)&&(ho(n.element(),e.stateAttr,yo(n.element(),e.destinationAttr)),wo(n.element(),e.destinationAttr));var u,a,c=(u=r,a=i,{start:yo(t.element(),u.stateAttr),destination:a});Zm(t,r,o,c).fold(function(){tg(t,r,o,i)},function(n){ng(t,r,o);var e=n.transition;Eo(t.element(),e.transitionClass),ho(t.element(),r.destinationAttr,i)})},getState:function(n,e,t){var r=n.element();return xo(r,e.stateAttr)?E.some(yo(r,e.stateAttr)):E.none()}}),og=/* */Object.freeze({events:function(o,i){return Gr([Wr(_(),function(t,n){var r=n.event().raw();eg(t,o,i).each(function(e){Qm(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(tg(t,o,i,e.destination),o.onTransition(t,e))})})})}),Kr(function(n,e){tg(n,o,i,o.initialState)})])}}),ig=[Mr("destinationAttr","data-transitioning-destination"),Mr("stateAttr","data-transitioning-state"),Or("initialState"),ci("onTransition"),ci("onFinish"),Tr("routes",pr(ft.value,pr(ft.value,ir([Dr("transition",[Or("property"),Or("transitionClass")])]))))],ug=mo({fields:ig,name:"transitioning",active:og,apis:rg,extra:{createRoutes:function(n){var r={};return M(n,function(n,e){var t=e.split("<->");r[t[0]]=Ct(t[1],n),r[t[1]]=Ct(t[0],n)}),r},createBistate:function(n,e,t){return Dt([{key:n,value:Ct(e,t)},{key:e,value:Ct(n,t)}])},createTristate:function(n,e,t,r){return Dt([{key:n,value:Dt([{key:e,value:r},{key:t,value:r}])},{key:e,value:Dt([{key:n,value:r},{key:t,value:r}])},{key:t,value:Dt([{key:n,value:r},{key:e,value:r}])}])}}}),ag=Fi.resolve("scrollable"),cg={register:function(n){Eo(n,ag)},deregister:function(n){Do(n,ag)},scrollable:h(ag)},fg=function(n){return Et(n,"format").getOr(n.title)},sg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[Fi.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:Fi.resolve("format-matches"),selected:t},itemBehaviours:so(o?[]:[Ai(n,function(n,e){(e?Mi.on:Mi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},lg=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[hf.sketch({dom:{tag:"div",classes:[Fi.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[Fi.resolve("styles-collapse-icon")]}},Sm(n)]:[Sm(n)],action:function(n){if(r){var e=t().get(n);Jm.collapseMenu(e)}}}),{dom:{tag:"div",classes:[Fi.resolve("styles-menu-items-container")]},components:[Pm.parts().items({})],behaviours:so([gd("adhoc-scrollable-menu",[Kr(function(n,e){Ui(n.element(),"overflow-y","auto"),Ui(n.element(),"-webkit-overflow-scrolling","touch"),cg.register(n.element())}),Jr(function(n){qi(n.element(),"overflow-y"),qi(n.element(),"-webkit-overflow-scrolling"),cg.deregister(n.element())})])])}],items:e,menuBehaviours:so([ug.config({initialState:"after",routes:ug.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},dg=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return a},n=lg("Styles",[].concat(Tn(o.items,function(n){return sg(fg(n),n.title,n.isSelected(),n.getPreview(),At(o.expansions,fg(n)))})),i,!1),e=A(o.menus,function(n,e){var t=Tn(n,function(n){return sg(fg(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",At(o.expansions,fg(n)))});return lg(e,t,i,!0)}),t=mt(e,Ct("styles",n)),{tmenu:Jm.tieredData("styles",t,o.expansions)}),a=zl(Jm.sketch({dom:{tag:"div",classes:[Fi.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=Yf.getValue(e);return r.handle(e,t.value),E.none()},onEscape:function(){return E.none()},onOpenMenu:function(n,e){var t=cs(n.element());as(e.element(),t),ug.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=cs(n.element()),o=ou(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();as(t.element(),r),ug.progressTo(i,"before"),ug.jumpTo(t,"after"),ug.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=ou(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();ug.progressTo(o,"after"),ug.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:u.tmenu,markers:{backgroundMenu:Fi.resolve("styles-background-menu"),menu:Fi.resolve("styles-menu"),selectedMenu:Fi.resolve("styles-selected-menu"),item:Fi.resolve("styles-item"),selectedItem:Fi.resolve("styles-selected-item")}}));return a.asSpec()},mg=function(n){return At(n,"items")?(i=mt((st([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),e=o=n,t=["items"],r={},M(e,function(n,e){Sn(t,e)||(r[e]=n)}),r),{menu:!0}),u=gg(o.items),{item:i,menus:mt(u.menus,Ct(o.title,u.items)),expansions:mt(u.expansions,Ct(o.title,o.title))}):{item:n,menus:{},expansions:{}};var e,t,r,o,i,u},gg=function(n){return Cn(n,function(n,e){var t=mg(e);return{menus:mt(n.menus,t.menus),items:[t.item].concat(n.items),expansions:mt(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},pg={expand:gg},vg=function(u,n){var a=function(n){return function(){return u.formatter.match(n)}},c=function(n){return function(){return u.formatter.getCssText(n)}},e=Et(n,"style_formats").getOr(Zd),f=function(n){return Tn(n,function(n){if(At(n,"items")){var e=f(n.items);return mt(mt(n,{isSelected:h(!1),getPreview:h("")}),{items:e})}return At(n,"format")?mt(i=n,{isSelected:a(i.format),getPreview:c(i.format)}):(r=Ec((t=n).title),o=mt(t,{format:r,isSelected:a(r),getPreview:c(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return f(e)},hg=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return Fn(n,function(n){return n.items===undefined?!At(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<o(n.items).length?[n]:[]})})(n),pg.expand(i));return dg({formats:u,handle:function(n,e){t.undoManager.transact(function(){Mi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},bg=["undo","bold","italic","link","image","bullist","styleselect"],yg=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},xg=function(n){return Fn(n,function(n){return hn(n)?xg(n):yg(n)})},wg=function(n){var e=n.toolbar!==undefined?n.toolbar:bg;return hn(e)?xg(e):yg(e)},Sg=function(r,o){var n=function(n){return function(){return kf.forToolbarCommand(o,n)}},e=function(n){return function(){return kf.forToolbarStateCommand(o,n)}},t=function(n,e,t){return function(){return kf.forToolbarStateAction(o,n,e,t)}},i=n("undo"),u=n("redo"),a=e("bold"),c=e("italic"),f=e("underline"),s=n("removeformat"),l=t("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=t("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=t("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=vg(o,o.settings),p=function(){return hg(o,g,function(){o.fire("scrollIntoView")})},v=function(n,e){return{isSupported:function(){return n.forall(function(n){return At(o.buttons,n)})},sketch:e}};return{undo:v(E.none(),i),redo:v(E.none(),u),bold:v(E.none(),a),italic:v(E.none(),c),underline:v(E.none(),f),removeformat:v(E.none(),s),link:v(E.none(),function(){return e=r,t=o,kf.forToolbarStateAction(t,"link","link",function(){var n=Qd(e,t);e.setContextToolbar(n),md(t,function(){e.focusToolbar()}),sd.query(t).each(function(n){t.selection.select(n.dom())})});var e,t}),unlink:v(E.none(),l),image:v(E.none(),function(){return od(o)}),bullist:v(E.some("bullist"),d),numlist:v(E.some("numlist"),m),fontsizeselect:v(E.none(),function(){return n={onChange:function(n){Nl.apply(e,n)},getInitialValue:function(){return Nl.get(e)}},Ml(r,"font-size",function(){return jl(n)},e=o);var e,n}),forecolor:v(E.none(),function(){return Il(r,o)}),styleselect:v(E.none(),function(){return kf.forToolbar("style-formats",function(n){o.fire("toReading"),r.dropup().appear(p,Mi.on,n)},so([Mi.config({toggleClass:Fi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),vi.config({channels:Dt([Bi(Uo.orientationChanged(),Mi.off),Bi(Uo.dropupDismissed(),Mi.off)])})]),o)})}},Og=function(n,t){var e=wg(n),r={};return Fn(e,function(n){var e=!At(r,n)&&At(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Tg=function(m,g){return function(n){if(m(n)){var e,t,r,o,i,u,a,c=be.fromDom(n.target),f=function(){n.stopPropagation()},s=function(){n.preventDefault()},l=p(s,f),d=(e=c,t=n.clientX,r=n.clientY,o=f,i=s,u=l,a=n,{target:h(e),x:h(t),y:h(r),stop:o,prevent:i,kill:u,raw:h(a)});g(d)}}},kg=function(n,e,t,r,o){var i=Tg(t,r);return n.dom().addEventListener(e,i,o),{unbind:l(Eg,n,e,i,o)}},Eg=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Cg=h(!0),Dg=function(n,e,t){return kg(n,e,Cg,t,!1)},Mg=function(n,e,t){return kg(n,e,Cg,t,!0)},Ag=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:h(e)}},Ig=Ag,Bg=function(r,e){var n=be.fromDom(r),o=null,t=Dg(n,"orientationchange",function(){clearInterval(o);var n=Ag(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){clearInterval(o);var e=r.innerHeight,t=0;o=setInterval(function(){e!==r.innerHeight?(clearInterval(o),n(E.some(r.innerHeight))):20<t&&(clearInterval(o),n(E.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Rg=function(n){var e=Yn.detect().os.isiOS(),t=Ag(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},Fg=function(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?E.none():E.some(e.touches[0])},Vg=function(t){var u=Fo(E.none()),r=function n(t,r){var o=null;return{cancel:function(){null!==o&&(v.clearTimeout(o),o=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];o=v.setTimeout(function(){t.apply(null,n),o=null},r)}}}(function(n){u.set(E.none()),t.triggerEvent(ie(),n)},400),o=Dt([{key:F(),value:function(t){return Fg(t).each(function(n){r.cancel();var e={x:h(n.clientX),y:h(n.clientY),target:t.target};r.schedule(t),u.set(E.some(e))}),E.none()}},{key:V(),value:function(n){return r.cancel(),Fg(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x()),o=Math.abs(e.clientY-t.y()),(5<r||5<o)&&u.set(E.none())})}),E.none()}},{key:N(),value:function(e){return r.cancel(),u.get().filter(function(n){return Ue(n.target(),e.target())}).map(function(n){return t.triggerEvent(re(),e)})}}]);return{fireIfReady:function(e,n){return Et(o,n).bind(function(n){return n(e)})}}},Ng=function(t){var e=Vg({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return Dg(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return Dg(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Hg=6<=Yn.detect().os.version.major,jg=function(r,e,t){var o=Ng(r),i=Ge(e),u=function(n){return!Ue(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||Ho(i).filter(function(n){return"input"===Se(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?Mi.on:Mi.off)},a=[Dg(r.body(),"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),Dg(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){No(r.body())}),r.onToEditing(x),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!==t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0===Hg?[]:[Dg(be.fromDom(r.win()),"blur",function(){t.getByDom(e).each(Mi.off)}),Dg(i,"select",n),Dg(r.doc(),"selectionchange",n)]);return{destroy:function(){kn(a,function(n){n.unbind()})}}},zg=function(n,e){var t=parseInt(yo(n,e),10);return isNaN(t)?0:t},Lg=function _h(t,r){var o=function(n){return t(n)?E.from(n.dom().nodeValue):E.none()},n=Yn.detect().browser,e=n.isIE()&&10===n.version.major?function(n){try{return o(n)}catch(e){return E.none()}}:o;return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(ke,"text"),Pg=function(n){return Lg.getOption(n)},Ug=st([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Gg={before:Ug.before,on:Ug.on,after:Ug.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(b,b,b)}},$g=st([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Wg=Me("start","soffset","finish","foffset"),_g=$g.relative,qg=$g.exact,Xg=function(n,e,t,r){var o,i,u,a,c,f=(i=e,u=t,a=r,(c=Ge(o=n).dom().createRange()).setStart(o.dom(),i),c.setEnd(u.dom(),a),c),s=Ue(n,t)&&e===r;return f.collapsed&&!s},Yg=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},Kg=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},Jg=function(n){return{left:h(n.left),top:h(n.top),right:h(n.right),bottom:h(n.bottom),width:h(n.width),height:h(n.height)}},Qg=st([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Zg=function(n,e,t){return e(be.fromDom(t.startContainer),t.startOffset,be.fromDom(t.endContainer),t.endOffset)},np=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:h(n),rtl:E.none}},relative:function(n,e){return{ltr:X(function(){return Yg(o,n,e)}),rtl:X(function(){return E.some(Yg(o,e,n))})}},exact:function(n,e,t,r){return{ltr:X(function(){return Kg(o,n,e,t,r)}),rtl:X(function(){return E.some(Kg(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Qg.rtl(be.fromDom(n.endContainer),n.endOffset,be.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Zg(0,Qg.ltr,r)}):Zg(0,Qg.ltr,r)},ep=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n,e){var t=Se(n);return"input"===t?Gg.after(n):Sn(["br","img"],t)?0===e?Gg.before(n):Gg.after(n):Gg.on(n,e)}),tp=function(n,e,t,r,o){var i,u,a=Kg(n,e,t,r,o);i=n,u=a,E.from(i.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(u)})},rp=function(n,e,t,r,o){var i,u,a,c,l,f=(i=r,u=o,a=ep(e,t),c=ep(i,u),_g(a,c));np(l=n,f).match({ltr:function(n,e,t,r){tp(l,n,e,t,r)},rtl:function(n,e,t,r){var o,i,u,a,c,f=l.getSelection();if(f.setBaseAndExtent)f.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(f.extend)try{i=n,u=e,a=t,c=r,(o=f).collapse(i.dom(),u),o.extend(a.dom(),c)}catch(s){tp(l,t,r,n,e)}else tp(l,t,r,n,e)}})},op=function(n){var e=be.fromDom(n.anchorNode),t=be.fromDom(n.focusNode);return Xg(e,n.anchorOffset,t,n.focusOffset)?E.some(Wg(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return E.some(Wg(be.fromDom(e.startContainer),e.startOffset,be.fromDom(t.endContainer),t.endOffset))}return E.none()}(n)},ip=function(n){return E.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(op)},up=function(n,e){var i,t,r,o,u=np(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?E.some(o).map(Jg):E.none()},ap=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:h(2),height:n.height}},cp=function(n){return{left:h(n.left),top:h(n.top),right:h(n.right),bottom:h(n.bottom),width:h(n.width),height:h(n.height)}},fp=function(r){if(r.collapsed){var o=be.fromDom(r.startContainer);return $e(o).bind(function(n){var e,t=qg(o,r.startOffset,n,"img"===Se(e=n)?1:Pg(e).fold(function(){return We(e).length},function(n){return n.length}));return up(r.startContainer.ownerDocument.defaultView,t).map(ap).map(jn)}).getOr([])}return Tn(r.getClientRects(),cp)},sp=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?fp(e.getRangeAt(0)):[]},lp=function(n){n.focus();var e=be.fromDom(n.document.body);(Ho().exists(function(n){return Sn(["input","textarea"],Se(n))})?function(n){setTimeout(function(){n()},0)}:s)(function(){Ho().each(No),Vo(e)})},dp="data-"+Fi.resolve("last-outer-height"),mp=function(n,e){ho(n,dp,e)},gp=function(n){return{top:h(n.top()),bottom:h(n.top()+n.height())}},pp=function(n,e){var t=zg(e,dp),r=n.innerHeight;return r<t?E.some(t-r):E.none()},vp=function(n,u){var e=be.fromDom(u.document.body),t=Dg(be.fromDom(n),"resize",function(){pp(n,e).each(function(i){var n,e;(n=u,e=sp(n),0<e.length?E.some(e[0]).map(gp):E.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(r,t.bottom()-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),mp(e,n.innerHeight)});return mp(e,n.innerHeight),{toEditing:function(){lp(u)},destroy:function(){t.unbind()}}},hp=function(n){return E.some(be.fromDom(n.dom().contentWindow.document.body))},bp=function(n){return E.some(be.fromDom(n.dom().contentWindow.document))},yp=function(n){return E.from(n.dom().contentWindow)},xp=function(n){return yp(n).bind(ip)},wp=function(n){return n.getFrame()},Sp=function(n,t){return function(e){return e[n].getOrThunk(function(){var n=wp(e);return function(){return t(n)}})()}},Op=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return Dg(e,r,n)}})},Tp=function(n){return{left:h(n.left),top:h(n.top),right:h(n.right),bottom:h(n.bottom),width:h(n.width),height:h(n.height)}},kp={getBody:Sp("getBody",hp),getDoc:Sp("getDoc",bp),getWin:Sp("getWin",yp),getSelection:Sp("getSelection",xp),getFrame:wp,getActiveApi:function(a){var c=wp(a);return hp(c).bind(function(u){return bp(c).bind(function(i){return yp(c).map(function(o){var n=be.fromDom(i.dom().documentElement),e=a.getCursorBox.getOrThunk(function(){return function(){return(n=o,ip(n).map(function(n){return qg(n.start(),n.soffset(),n.finish(),n.foffset())})).bind(function(n){return up(o,n).orThunk(function(){return ip(o).filter(function(n){return Ue(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?E.some(e).map(Tp):E.none()})})});var n}}),t=a.setSelection.getOrThunk(function(){return function(n,e,t,r){rp(o,n,e,t,r)}}),r=a.clearSelection.getOrThunk(function(){return function(){o.getSelection().removeAllRanges()}});return{body:h(u),doc:h(i),win:h(o),html:h(n),getSelection:l(xp,c),setSelection:t,clearSelection:r,frame:h(c),onKeyup:Op(a,i,"onKeyup","keyup"),onNodeChanged:Op(a,i,"onNodeChanged","selectionchange"),onDomChanged:a.onDomChanged,onScrollToCursor:a.onScrollToCursor,onScrollToElement:a.onScrollToElement,onToReading:a.onToReading,onToEditing:a.onToEditing,onToolbarScrollStart:a.onToolbarScrollStart,onTouchContent:a.onTouchContent,onTapContent:a.onTapContent,onTouchToolstrip:a.onTouchToolstrip,getCursorBox:e}})})})}},Ep="data-ephox-mobile-fullscreen-style",Cp="position:absolute!important;",Dp="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",Mp=Yn.detect().os.isAndroid(),Ap=function(n,e){var t,r,o,i=function(r){return function(n){var e=yo(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(ho(n,Ep,t),ho(n,"style",r))}},u=(t="*",nu(n,function(n){return je(n,t)},r)),a=Fn(u,function(n){var e;return e="*",eu(n,function(n){return je(n,e)})}),c=(o=$i(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";kn(a,i("display:none!important;")),kn(u,i(Cp+Dp+c)),i((!0===Mp?"":Cp)+Dp+c)(n)},Ip=function(){var n=Le("["+Ep+"]");kn(n,function(n){var e=yo(n,Ep);"no-styles"!==e?ho(n,"style",e):wo(n,"style"),wo(n,Ep)})},Bp=function(){var e=ru("head").getOrDie(),n=ru('meta[name="viewport"]').getOrThunk(function(){var n=be.fromTag("meta");return ho(n,"name","viewport"),Ke(e,n),n}),t=yo(n,"content");return{maximize:function(){ho(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?ho(n,"content",t):ho(n,"content","user-scalable=yes")}}},Rp=function(e,n){var t=Bp(),r=_d(),o=_d();return{enter:function(){n.hide(),Eo(e.container,Fi.resolve("fullscreen-maximized")),Eo(e.container,Fi.resolve("android-maximized")),t.maximize(),Eo(e.body,Fi.resolve("android-scroll-reload")),r.set(vp(e.win,kp.getWin(e.editor).getOrDie("no"))),kp.getActiveApi(e.editor).each(function(n){Ap(e.container,n.body()),o.set(jg(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),Do(e.container,Fi.resolve("fullscreen-maximized")),Do(e.container,Fi.resolve("android-maximized")),Ip(),Do(e.body,Fi.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Fp=function(t,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&clearTimeout(o),o=setTimeout(function(){t.apply(null,n),o=null},r)}}},Vp=function(n,e){var t,r,o,i=zl(bd.sketch({dom:xf('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:so([Mi.config({toggleClass:Fi.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=setTimeout(function(){t.apply(null,n),o=null},r))}});return bd.sketch({dom:xf('<div class="${prefix}-disabled-mask"></div>'),components:[bd.sketch({dom:xf('<div class="${prefix}-content-container"></div>'),components:[hf.sketch({dom:xf('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:so([Mi.config({toggleClass:Fi.resolve("mask-tap-icon-selected")})])})]})]})},Np=ur([kr("editor",[Or("getFrame"),Er("getBody"),Er("getDoc"),Er("getWin"),Er("getSelection"),Er("setSelection"),Er("clearSelection"),Er("cursorSaver"),Er("onKeyup"),Er("onNodeChanged"),Er("getCursorBox"),Or("onDomChanged"),Mr("onTouchContent",x),Mr("onTapContent",x),Mr("onTouchToolstrip",x),Mr("onScrollToCursor",h({unbind:x})),Mr("onScrollToElement",h({unbind:x})),Mr("onToEditing",h({unbind:x})),Mr("onToReading",h({unbind:x})),Mr("onToolbarScrollStart",b)]),Or("socket"),Or("toolstrip"),Or("dropup"),Or("toolbar"),Or("container"),Or("alloy"),Ir("win",function(n){return Ge(n.socket).dom().defaultView}),Ir("body",function(n){return be.fromDom(n.socket.dom().ownerDocument.body)}),Mr("translate",b),Mr("setReadOnly",x),Mr("readOnlyOnInit",h(!0))]),Hp=function(n){var e=br("Getting AndroidWebapp schema",Np,n);Ui(e.toolstrip,"width","100%");var t=km(Vp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Ke(e.container,t.element());var o=Rp(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:x,enter:o.enter,exit:o.exit,destroy:x}},jp=h([Mr("shell",!0),dc("toolbarBehaviours",[_m])]),zp=h([Pc({name:"groups",overrides:function(n){return{behaviours:so([_m.config({})])}}})]),Lp=vf({name:"Toolbar",configFields:jp(),partFields:zp(),factory:function(e,n,t,r){var o=function(n){return e.shell?E.some(n):Yc(n,e,"groups")},i=e.shell?{behaviours:[_m.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:gc(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){_m.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Pp=h([Or("items"),(ed=["itemSelector"],kr("markers",Tn(ed,Or))),dc("tgroupBehaviours",[lc])]),Up=h([Uc({name:"items",unit:"item"})]),Gp=vf({name:"ToolbarGroup",configFields:Pp(),partFields:Up(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:gc(n.tgroupBehaviours,[lc.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),$p="data-"+Fi.resolve("horizontal-scroll"),Wp=function(n){return"true"===yo(n,$p)?0<(t=n).dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(t):0<(e=n).dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(e);var e,t},_p={exclusive:function(n,e){return Dg(n,"touchmove",function(n){uu(n.target(),e).filter(Wp).fold(function(){n.raw().preventDefault()},x)})},markAsHorizontal:function(n){ho(n,$p,"true")}};function qp(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:xf('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:so([gd("adhoc-scrollable-toolbar",!0===n.scrollable?[Qr(function(n,e){Ui(n.element(),"overflow-x","auto"),_p.markAsHorizontal(n.element()),cg.register(n.element())})]:[])]),components:[bd.sketch({components:[Gp.parts().items({})]})],markers:{itemSelector:"."+Fi.resolve("toolbar-group-item")},items:n.items}},t=km(Lp.sketch({dom:xf('<div class="${prefix}-toolbar"></div>'),components:[Lp.parts().groups({})],toolbarBehaviours:so([Mi.config({toggleClass:Fi.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),lc.config({mode:"cyclic"})]),shell:!0})),n=km(bd.sketch({dom:{classes:[Fi.resolve("toolstrip")]},components:[Em(t)],containerBehaviours:so([Mi.config({toggleClass:Fi.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Lp.setGroups(t,o.get()),Mi.off(t)},o=Fo([]);return{wrapper:h(n),toolbar:h(t),createGroups:function(n){return Tn(n,p(Gp.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){Mi.on(t),Lp.setGroups(t,n)},restoreToolbar:function(){Mi.isOn(t)&&r()},refresh:function(){},focus:function(){lc.focusIn(t)}}}var Xp,Yp=function(n,e){_m.append(n,Em(e))},Kp=function(n,e){_m.remove(n,e)},Jp=function(n){return km(hf.sketch({dom:xf('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},Qp=function(){return km(bd.sketch({dom:xf('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:so([_m.config({})])}))},Zp=function(n,e,t,r){(!0===t?Ro.toAlpha:Ro.toOmega)(r),(t?Yp:Kp)(n,e)},nv=function(e,n){return n.getAnimationRoot.fold(function(){return e.element()},function(n){return n(e)})},ev=function(n){return n.dimension.property},tv=function(n,e){return n.dimension.getDimension(e)},rv=function(n,e){var t=nv(n,e);pm(t,[e.shrinkingClass,e.growingClass])},ov=function(n,e){Do(n.element(),e.openClass),Eo(n.element(),e.closedClass),Ui(n.element(),ev(e),"0px"),Xi(n.element())},iv=function(n,e){Do(n.element(),e.closedClass),Eo(n.element(),e.openClass),qi(n.element(),ev(e))},uv=function(n,e,t,r){t.setCollapsed(),Ui(n.element(),ev(e),tv(e,n.element())),Xi(n.element()),rv(n,e),ov(n,e),e.onStartShrink(n),e.onShrunk(n)},av=function(n,e,t,r){var o=r.getOrThunk(function(){return tv(e,n.element())});t.setCollapsed(),Ui(n.element(),ev(e),o),Xi(n.element());var i=nv(n,e);Do(i,e.growingClass),Eo(i,e.shrinkingClass),ov(n,e),e.onStartShrink(n)},cv=function(n,e,t){var r=tv(e,n.element());("0px"===r?uv:av)(n,e,t,E.some(r))},fv=function(n,e,t){var r=nv(n,e),o=Mo(r,e.shrinkingClass),i=tv(e,n.element());iv(n,e);var u=tv(e,n.element());(o?function(){Ui(n.element(),ev(e),i),Xi(n.element())}:function(){ov(n,e)})(),Do(r,e.shrinkingClass),Eo(r,e.growingClass),iv(n,e),Ui(n.element(),ev(e),u),t.setExpanded(),e.onStartGrow(n)},sv=function(n,e,t){var r=nv(n,e);return!0===Mo(r,e.growingClass)},lv=function(n,e,t){var r=nv(n,e);return!0===Mo(r,e.shrinkingClass)},dv=/* */Object.freeze({refresh:function(n,e,t){if(t.isExpanded()){qi(n.element(),ev(e));var r=tv(e,n.element());Ui(n.element(),ev(e),r)}},grow:function(n,e,t){t.isExpanded()||fv(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&cv(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&uv(n,e,t,E.none())},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:sv,isShrinking:lv,isTransitioning:function(n,e,t){return!0===sv(n,e)||!0===lv(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?cv:fv)(n,e,t)},disableTransitions:rv}),mv=/* */Object.freeze({exhibit:function(n,e){var t=e.expanded;return to(t?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:Ct(e.dimension.property,"0px")})},events:function(t,r){return Gr([(n=_(),e=function(n,e){e.event().raw().propertyName===t.dimension.property&&(rv(n,t),r.isExpanded()&&qi(n.element(),t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))},qr(n)(e))]);var n,e}}),gv=[Or("closedClass"),Or("openClass"),Or("shrinkingClass"),Or("growingClass"),Er("getAnimationRoot"),ci("onShrunk"),ci("onStartShrink"),ci("onGrown"),ci("onStartGrow"),Mr("expanded",!1),Tr("dimension",xr("property",{width:[di("property","width"),di("getDimension",function(n){return cs(n)+"px"})],height:[di("property","height"),di("getDimension",function(n){return Zi(n)+"px"})]}))],pv=mo({fields:gv,name:"sliding",active:mv,apis:dv,state:/* */Object.freeze({init:function(n){var e=Fo(n.expanded);return fo({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:l(e.set,!1),setExpanded:l(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),vv=function(e,t){var r=km(bd.sketch({dom:{tag:"div",classes:[Fi.resolve("dropup")]},components:[],containerBehaviours:so([_m.config({}),pv.config({closedClass:Fi.resolve("dropup-closed"),openClass:Fi.resolve("dropup-open"),shrinkingClass:Fi.resolve("dropup-shrinking"),growingClass:Fi.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),_m.set(n,[])},onGrown:function(n){e(),t()}}),Ii(function(n,e){o(x)})])})),o=function(n){v.window.requestAnimationFrame(function(){n(),pv.shrink(r)})};return{appear:function(n,e,t){!0===pv.hasShrunk(r)&&!1===pv.isTransitioning(r)&&v.window.requestAnimationFrame(function(){e(t),_m.set(r,[n()]),pv.grow(r)})},disappear:o,component:h(r),element:r.element}},hv=function(n){var e,t;return 8===n.raw().which&&!Sn(["input","textarea"],Se(n.target()))&&(e=n.target(),!uu(e,'[contenteditable="true"]',t).isSome())},bv=Yn.detect().browser.isFirefox(),yv=ir([(Xp="triggerEvent",Tr(Xp,Sr)),Mr("stopBackspace",!0)]),xv=function(e,n){var t,r,o,i,u=br("Getting GUI events settings",yv,n),a=Yn.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],c=Vg(u),f=Tn(a.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Dg(e,n,function(e){c.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),s=Fo(E.none()),l=Dg(e,"paste",function(e){c.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),u.triggerEvent("paste",e)&&e.kill(),s.set(E.some(v.setTimeout(function(){u.triggerEvent(Zn(),e)},0)))}),d=Dg(e,"keydown",function(n){u.triggerEvent("keydown",n)?n.kill():!0===u.stopBackspace&&hv(n)&&n.prevent()}),m=(t=e,r=function(n){u.triggerEvent("focusin",n)&&n.kill()},bv?Mg(t,"focus",r):Dg(t,"focusin",r)),g=Fo(E.none()),p=(o=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),g.set(E.some(v.setTimeout(function(){u.triggerEvent(Qn(),n)},0)))},bv?Mg(o,"blur",i):Dg(o,"focusout",i));return{unbind:function(){kn(f,function(n){n.unbind()}),d.unbind(),m.unbind(),p.unbind(),l.unbind(),s.get().each(v.clearTimeout),g.get().each(v.clearTimeout)}}},wv=function(n,e){var t=Et(n,"target").map(function(n){return n()}).getOr(e);return Fo(t)},Sv=st([{stopped:[]},{resume:["element"]},{complete:[]}]),Ov=function(n,r,e,t,o,i){var u,a,c,f,s=n(r,t),l=(u=e,a=o,c=Fo(!1),f=Fo(!1),{stop:function(){c.set(!0)},cut:function(){f.set(!0)},isStopped:c.get,isCut:f.get,event:h(u),setSource:a.set,getSource:a.get});return s.fold(function(){return i.logEventNoHandlers(r,t),Sv.complete()},function(e){var t=e.descHandler();return am(t)(l),l.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),Sv.stopped()):l.isCut()?(i.logEventCut(r,e.element(),t.purpose()),Sv.complete()):$e(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),Sv.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),Sv.resume(n)})})},Tv=function(e,t,r,n,o,i){return Ov(e,t,r,n,o,i).fold(function(){return!0},function(n){return Tv(e,t,r,n,o,i)},function(){return!1})},kv=function(n,e,t){var r,o,i=(r=e,o=Fo(!1),{stop:function(){o.set(!0)},cut:x,isStopped:o.get,isCut:h(!1),event:h(r),setSource:f("Cannot set source of a broadcasted event"),getSource:f("Cannot get source of a broadcasted event")});return kn(n,function(n){var e=n.descHandler();am(e)(i)}),i.isStopped()},Ev=function(n,e,t,r,o){var i=wv(t,r);return Tv(n,e,t,r,i,o)},Cv=Me("element","descHandler"),Dv=function(n,e){return{id:h(n),descHandler:h(e)}};function Mv(){var i={};return{registerId:function(r,o,n){M(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=um(n,r),i[e]=t})},unregisterId:function(t){M(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return Et(i,n).map(function(n){return B(n,function(n,e){return Dv(e,n)})}).getOr([])},find:function(n,e,t){var o=Tt(e)(i);return Ur(t,function(n){return t=o,cf(r=n).fold(function(){return E.none()},function(n){var e=Tt(n);return t.bind(e).map(function(n){return Cv(r,n)})});var t,r},n)}}}function Av(){var r=Mv(),o={},i=function(r){var n=r.element();return cf(n).fold(function(){return n="uid-",e=r.element(),t=Ec(of+n),af(e,t),t;var n,e,t},function(n){return n})},u=function(n){cf(n.element()).each(function(n){o[n]=undefined,r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);At(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+qo(t.element())+"\nCannot use it for: "+qo(n.element())+"\nThe conflicting element is"+(Ee(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events()),o[e]=n},unregister:u,getById:function(n){return Tt(n)(o)}}}var Iv=function(t){var r=function(e){return $e(t.element()).fold(function(){return!0},function(n){return Ue(e,n)})},o=Av(),f=function(n,e){return o.find(r,n,e)},n=xv(t.element(),{triggerEvent:function(u,a){return ei(u,a.target(),function(n){return e=f,t=u,o=n,i=(r=a).target(),Ev(e,t,r,i,o);var e,t,r,o,i})}}),i={debugInfo:h("real"),triggerEvent:function(e,t,r){ei(e,t,function(n){Ev(f,e,r,t,n)})},triggerFocus:function(a,c){cf(a).fold(function(){Vo(a)},function(n){ei(Jn(),a,function(n){var e,t,r,o,i,u;e=f,t=Jn(),r={originator:h(c),kill:x,prevent:x,target:h(a)},i=n,u=wv(r,o=a),Ov(e,t,r,o,u,i)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:km,addToGui:function(n){a(n)},removeFromGui:function(n){c(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:h(!0)},e=function(n){n.connect(i),ke(n.element())||(o.register(n),kn(n.components(),e),i.triggerEvent(ue(),n.element(),{target:h(n.element())}))},u=function(n){ke(n.element())||(kn(n.components(),u),o.unregister(n)),n.disconnect()},a=function(n){tt(t,n)},c=function(n){it(n)},s=function(t){var n=o.filter(ne());kn(n,function(n){var e=n.descHandler();am(e)(t)})},l=function(n){s({universal:h(!0),data:h(n)})},d=function(n,e){s({universal:h(!1),channels:h(n),data:h(e)})},m=function(n,e){var t=o.filter(n);return kv(t,e)},g=function(n){return o.getById(n).fold(function(){return ft.error(new Error('Could not find component with uid: "'+n+'" in system.'))},ft.value)},p=function(n){var e=cf(n).getOr("not found");return g(e)};return e(t),{root:h(t),element:t.element,destroy:function(){n.unbind(),Ze(t.element())},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d,broadcastEvent:m}},Bv=h(Fi.resolve("readonly-mode")),Rv=h(Fi.resolve("edit-mode"));function Fv(n){var e=km(bd.sketch({dom:{classes:[Fi.resolve("outer-container")].concat(n.classes)},containerBehaviours:so([Ro.config({alpha:Bv(),omega:Rv()})])}));return Iv(e)}var Vv=function(n,e){var t=be.fromTag("input");Gi(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Ke(n,t),Vo(t),e(t),Ze(t)},Nv=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Hv=function(n,e){Ho().each(function(n){Ue(n,e)||No(n)}),n.focus(),Vo(be.fromDom(n.document.body)),Nv(n)},jv={stubborn:function(n,e,t,r){var o=function(){Hv(e,r)},i=Dg(t,"keydown",function(n){Sn(["input","textarea"],Se(n.target()))||o()});return{toReading:function(){Vv(n,No)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){var o=function(){No(r)};return{toReading:function(){o()},toEditing:function(){Hv(e,r)},onToolbarTouch:function(){o()},destroy:x}}},zv=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},a=function(){r.run(function(n){n.clearSelection()})},c=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),r.run(function(n){n.syncHeight()})},f=Ng(t),s=Fp(c,300),l=[t.onKeyup(function(){a(),s.throttle()}),t.onNodeChanged(u),t.onDomChanged(s.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),t.onScrollToElement(function(n){n.element(),e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),Dg(t.doc(),"touchend",function(n){Ue(t.html(),n.target())||Ue(t.body(),n.target())}),Dg(o,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Zi(o),r.run(function(n){n.setViewportOffset(e)}),u(),c())}),Mg(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),Dg(t.body(),"touchstart",function(n){a(),t.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),Dg(t.body(),"click",function(n){n.kill()}),Dg(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){kn(l,function(n){n.unbind()})}}},Lv=function(n){var t=E.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){kn(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){t=E.some(n),i(e),e=[]}),{get:r,map:function(t){return Lv(function(e){r(function(n){e(t(n))})})},isReady:o}},Pv={nu:Lv,pure:function(e){return Lv(function(n){n(e)})}},Uv=function(e){var n=function(n){var r;e((r=n,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=this;setTimeout(function(){r.apply(t,n)},0)}))},t=function(){return Pv.nu(n)};return{map:function(r){return Uv(function(t){n(function(n){var e=r(n);t(e)})})},bind:function(t){return Uv(function(e){n(function(n){t(n).get(e)})})},anonBind:function(t){return Uv(function(e){n(function(n){t.get(e)})})},toLazy:t,toCached:function(){var e=null;return Uv(function(n){null===e&&(e=t()),e.get(n)})},get:n}},Gv={nu:Uv,pure:function(e){return Uv(function(n){n(e)})}},$v=function(n,e,t){return Math.abs(n-e)<=t?E.none():n<e?E.some(n+t):E.some(n-t)},Wv=function(){var f=null;return{animate:function(r,o,n,i,e,t){var u=!1,a=function(n){u=!0,e(n)};clearInterval(f);var c=function(n){clearInterval(f),a(n)};f=setInterval(function(){var t=r();$v(t,o,n).fold(function(){clearInterval(f),a(o)},function(n){if(i(n,c),!u){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(clearInterval(f),a(o))}})},t)}}},_v=function(e,t){return Ko([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e<=n.width&&t<=n.height?E.some(n.keyboard):E.none()}).getOr({portrait:t/5,landscape:e/4})},qv=function(n){var e,t=Ig(n).isPortrait(),r=_v((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},Xv=function(n,e){var t=Ge(n).dom().defaultView;return Zi(n)+Zi(e)-qv(t)},Yv=Xv,Kv=function(n,e,t){var r=Xv(e,t),o=Zi(e)+Zi(t)-r;Ui(n,"padding-bottom",o+"px")},Jv=st([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Qv="data-"+Fi.resolve("position-y-fixed"),Zv="data-"+Fi.resolve("y-property"),nh="data-"+Fi.resolve("scrolling"),eh="data-"+Fi.resolve("last-window-height"),th=function(n){return zg(n,Qv)},rh=function(n,e){var t=yo(n,Zv);return Jv.fixed(n,t,e)},oh=function(n,e){return Jv.scroller(n,e)},ih=function(n){var e=th(n);return("true"===yo(n,nh)?oh:rh)(n,e)},uh=function(n,e,t){var r=Ge(n).dom().defaultView.innerHeight;return ho(n,eh,r+"px"),r-e-t},ah=function(n){var e=tu(n,"["+Qv+"]");return Tn(e,ih)},ch=function(r,o,i,u){var n,e,t,a,c,f,s,l,d=Ge(r).dom().defaultView,m=(l=yo(s=i,"style"),Gi(s,{position:"absolute",top:"0px"}),ho(s,Qv,"0px"),ho(s,Zv,"top"),{restore:function(){ho(s,"style",l||""),wo(s,Qv),wo(s,Zv)}}),g=Zi(i),p=Zi(u),v=uh(r,g,p),h=(t=g,a=v,f=yo(c=r,"style"),cg.register(c),Gi(c,{position:"absolute",height:a+"px",width:"100%",top:t+"px"}),ho(c,Qv,t+"px"),ho(c,nh,"true"),ho(c,Zv,"top"),{restore:function(){cg.deregister(c),ho(c,"style",f||""),wo(c,Qv),wo(c,nh),wo(c,Zv)}}),b=(e=yo(n=u,"style"),Gi(n,{position:"absolute",bottom:"0px"}),ho(n,Qv,"0px"),ho(n,Zv,"bottom"),{restore:function(){ho(n,"style",e||""),wo(n,Qv),wo(n,Zv)}}),y=!0,x=function(){var n=d.innerHeight;return zg(r,eh)<n},w=function(){if(y){var n=Zi(i),e=Zi(u),t=uh(r,n,e);ho(r,Qv,n+"px"),Ui(r,"height",t+"px"),Ui(u,"bottom",-(n+t+e)+"px"),Kv(o,r,u)}};return Kv(o,r,u),{setViewportOffset:function(n){ho(r,Qv,n+"px"),w()},isExpanding:x,isShrinking:S(x),refresh:w,restore:function(){y=!1,m.restore(),h.restore(),b.restore()}}},fh=th,sh=Wv(),lh="data-"+Fi.resolve("last-scroll-top"),dh=function(n){var e=_i(n,"top").getOr("0");return parseInt(e,10)},mh=function(n){return parseInt(n.dom().scrollTop,10)},gh=function(n,e){var t=e+fh(n)+"px";Ui(n,"top",t)},ph=function(t,r,o){return Gv.nu(function(n){var e=l(mh,t);sh.animate(e,r,15,function(n){t.dom().scrollTop=n,Ui(t,"top",dh(t)+15+"px")},function(){t.dom().scrollTop=r,Ui(t,"top",o+"px"),n(r)},10)})},vh=function(o,i){return Gv.nu(function(n){var e=l(mh,o);ho(o,lh,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);sh.animate(e,i,r,function(n,e){zg(o,lh)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,ho(o,lh,n))},function(){o.dom().scrollTop=i,ho(o,lh,i),n(i)},10)})},hh=function(i,u){return Gv.nu(function(n){var e=l(dh,i),t=function(n){Ui(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);sh.animate(e,u,o,t,function(){t(u),n(u)},10)})},bh=function(e,t,r){var o=Ge(e).dom().defaultView;return Gv.nu(function(n){gh(e,r),gh(t,r),o.scrollTo(0,r),n(r)})},yh=function(n,e,t,r,o){var i=Yv(e,t),u=l(Nv,n);i<r||i<o?vh(e,e.dom().scrollTop-i+o).get(u):r<0&&vh(e,e.dom().scrollTop+r).get(u)},xh=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):kn(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},wh=function(n,c){return n.fold(function(n,e,t){return Ui(n,e,c+(r=t)+"px"),Gv.pure(r);var r},function(n,e){return o=c+(r=e),i=_i(t=n,"top").getOr(r),u=o-parseInt(i,10),a=t.dom().scrollTop+u,ph(t,a,o);var t,r,o,i,u,a})},Sh=function(n,e){var t=ah(n),r=Tn(t,function(n){return wh(n,e)});return xh(r,Gv.nu)},Oh=function(e,t,n,r,o,i){var u=function f(t){var r=Fo(Pv.pure({}));return{start:function(e){var n=Pv.nu(function(n){return t(e).get(n)});r.set(n)},idle:function(n){r.get().get(function(){n()})}}}(function(n){return bh(e,t,n)}),a=Fp(function(){u.idle(function(){Sh(n,r.pageYOffset).get(function(){var n;(n=sp(i),E.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?E.some({top:h(e),bottom:h(e+n.height())}):E.none()})).each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),u.start(0),o.refresh()})})},1e3),c=Dg(be.fromDom(r),"scroll",function(){r.pageYOffset<0||a.throttle()});return Sh(n,r.pageYOffset).get(b),{unbind:c.unbind}},Th=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),a=n.keyboardType(),c=n.outerWindow(),f=n.dropup(),s=ch(r,e,o,f),l=a(n.outerBody(),t,Ce(),u,o,i),d=Bg(c,{onChange:x,onReady:s.refresh});d.onAdjustment(function(){s.refresh()});var m=Dg(be.fromDom(c),"resize",function(){s.isExpanding()&&s.refresh()}),g=Oh(o,r,n.outerBody(),c,s,t),p=function h(t,e){var n=t.document,r=be.fromTag("div");Eo(r,Fi.resolve("unfocused-selections")),Ke(be.fromDom(n.documentElement),r);var o=Dg(r,"touchstart",function(n){n.prevent(),Hv(t,e),u()}),i=function(n){var e=be.fromTag("span");return gm(e,[Fi.resolve("layer-editor"),Fi.resolve("unfocused-selection")]),Gi(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){Qe(r)};return{update:function(){u();var n=sp(t),e=Tn(n,i);Je(r,e)},isActive:function(){return 0<We(r).length},destroy:function(){o.unbind(),Ze(r)},clear:u}}(t,u),v=function(){p.clear()};return{toEditing:function(){l.toEditing(),v()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:v,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){yh(t,r,f,n,e)},updateToolbarPadding:x,setViewportOffset:function(n){s.setViewportOffset(n),hh(r,n).get(b)},syncHeight:function(){Ui(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:s.refresh,destroy:function(){s.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),Vv(Ce(),No)}}},kh=function(r,n){var o=Bp(),i=qd(),u=qd(),a=_d(),c=_d();return{enter:function(){n.hide();var t=be.fromDom(v.document);kp.getActiveApi(r.editor).each(function(n){i.set({socketHeight:_i(r.socket,"height"),iframeHeight:_i(n.frame(),"height"),outerScroll:v.document.body.scrollTop}),u.set({exclusives:_p.exclusive(t,"."+cg.scrollable())}),Eo(r.container,Fi.resolve("fullscreen-maximized")),Ap(r.container,n.body()),o.maximize(),Ui(r.socket,"overflow","scroll"),Ui(r.socket,"-webkit-overflow-scrolling","touch"),Vo(n.body());var e=Be(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);a.set(Th(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:x,outerBody:r.body,outerWindow:r.win,keyboardType:jv.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),a.run(function(n){n.syncHeight()}),c.set(zv(n,a,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){a.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),c.clear(),a.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Ui(r.socket,"height",n)}),n.iframeHeight.each(function(n){Ui(r.editor.getFrame(),"height",n)}),v.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),Do(r.container,Fi.resolve("fullscreen-maximized")),Ip(),cg.deregister(r.toolbar),qi(r.socket,"overflow"),qi(r.socket,"-webkit-overflow-scrolling"),No(r.editor.getFrame()),kp.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Eh=function(n){var e=br("Getting IosWebapp schema",Np,n);Ui(e.toolstrip,"width","100%"),Ui(e.container,"position","relative");var t=km(Vp(function(){e.setReadOnly(e.readOnlyOnInit()),r.enter()},e.translate));e.alloy.add(t);var r=kh(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:r.refreshStructure,enter:r.enter,exit:r.exit,destroy:x}},Ch=tinymce.util.Tools.resolve("tinymce.EditorManager"),Dh=function(n){var e=Et(n.settings,"skin_url").fold(function(){return Ch.baseURL+"/skins/ui/oxide"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},Mh=function(n,e,t){n.system().broadcastOn([Uo.formatChanged()],{command:e,state:t})},Ah=function(r,n){var e=C(n.formatter.get());kn(e,function(e){n.formatter.formatChanged(e,function(n){Mh(r,e,n)})}),kn(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Mh(r,t,n)})})},Ih=(h(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),Bh=h("toReading"),Rh=h("toEditing"),Fh=function(p){return{getNotificationManagerImpl:function(){return{open:h({progressBar:{value:x},close:x}),close:x,reposition:x,getArgs:b}},renderUI:function(){var n=p.getElement(),e=Dh(p);0==(!1===p.settings.skin)?(p.contentCSS.push(e.content),zo.DOM.styleSheetLoader.load(e.ui,Ih(p))):Ih(p)();var t,r,o=function(){p.fire("scrollIntoView")},i=be.fromTag("div"),f=Yn.detect().os.isAndroid()?function m(n){var e=Fv({classes:[Fi.resolve("android-container")]}),t=qp(),r=_d(),o=Jp(r),i=Qp(),u=vv(x,n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:h(e),element:e.element,init:function(n){r.set(Hp(n))},exit:function(){r.run(function(n){n.exit(),_m.remove(i,o)})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Zp(i,o,n,e.root())},socket:h(i),dropup:h(u)}}(o):function g(n){var e=Fv({classes:[Fi.resolve("ios-container")]}),t=qp(),r=_d(),o=Jp(r),i=Qp(),u=vv(function(){r.run(function(n){n.refreshStructure()})},n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:h(e),element:e.element,init:function(n){r.set(Eh(n))},exit:function(){r.run(function(n){_m.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Zp(i,o,n,e.root())},socket:h(i),dropup:h(u)}}(o),u=be.fromDom(n);Xe(u,i),t=i,r=f.system(),ut(t,r,Ke);var a=n.ownerDocument.defaultView,s=Bg(a,{onChange:function(){f.system().broadcastOn([Uo.orientationChanged()],{width:Rg(a)})},onReady:x}),l=function(n,e,t,r){!1===r&&p.selection.collapse();var o=c(n,e,t);f.setToolbarGroups(!0===r?o.readOnly:o.main),p.setMode(!0===r?"readonly":"design"),p.fire(!0===r?Bh():Rh()),f.updateMode(r)},c=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},d=function(n,e){return p.on(n,e),{unbind:function(){p.off(n)}}};return p.on("init",function(){f.init({editor:{getFrame:function(){return be.fromDom(p.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:x}},onToReading:function(n){return d(Bh(),n)},onToEditing:function(n){return d(Rh(),n)},onScrollToCursor:function(e){return p.on("scrollIntoView",function(n){e(n)}),{unbind:function(){p.off("scrollIntoView"),s.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n,e=be.fromDom(p.editorContainer.querySelector("."+Fi.resolve("toolbar")));(n=e,jo(n).bind(function(n){return f.system().getByDom(n).toOption()})).each(ge),f.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();"img"===Se(e)?(p.selection.select(e.dom()),n.kill()):"a"===Se(e)&&f.system().getByDom(be.fromDom(p.editorContainer)).each(function(n){Ro.isAlpha(n)&&Po(e.dom())})}},container:be.fromDom(p.editorContainer),socket:be.fromDom(p.contentAreaContainer),toolstrip:be.fromDom(p.editorContainer.querySelector("."+Fi.resolve("toolstrip"))),toolbar:be.fromDom(p.editorContainer.querySelector("."+Fi.resolve("toolbar"))),dropup:f.dropup(),alloy:f.system(),translate:x,setReadOnly:function(n){l(c,a,u,n)},readOnlyOnInit:function(){return!1}});var t=function(){f.dropup().disappear(function(){f.system().broadcastOn([Uo.dropupDismissed()],{})})},n={label:"The first group",scrollable:!1,items:[kf.forToolbar("back",function(){p.selection.collapse(),f.exit()},{},p)]},e={label:"Back to read only",scrollable:!1,items:[kf.forToolbar("readonly-back",function(){l(c,a,u,!0)},{},p)]},r=Sg(f,p),o=Og(p.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=Fo([{label:"the action group",scrollable:!0,items:o},i]),a=Fo([{label:"The read only mode group",scrollable:!0,items:[]},i]),c=Fo({backToMask:[n],backToReadOnly:[e]});Ah(f,p)}),{iframeContainer:f.socket().element().dom(),editorContainer:f.element().dom()}}}};Lo.add("mobile",Fh),n.renderMobileTheme=Fh,n["default"]=function qh(){}}({},window);